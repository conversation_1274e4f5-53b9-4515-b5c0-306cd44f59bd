{"swagger": "2.0", "info": {"description": "Skywind - Galaxy Pro - API", "version": "5.55", "title": "Skywind - Galaxy Pro - API"}, "basePath": "/v1", "produces": ["application/json"], "securityDefinitions": {"apiKey": {"description": "Basic JWT authorization", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-access-token", "in": "header"}, "Permissions": {"description": "Dummy OAuth2 authorization for permissions through scopes", "type": "oauth2", "authorizationUrl": "http://localhost:3000/oauth/dialog", "tokenUrl": "http://localhost:3000/oauth/token", "flow": "accessCode", "scopes": {"live-chat": "Ability to have live chat in BO", "entity": "Entity management", "entity:edit": "Update entity", "entity:create": "Create entity", "entity:change-state": "Change status of entity", "entity:change-state-test": "Change 'test' status of entity", "entity:game:limits": "Apply game limit filters for entity and child entities", "entity:view": "View entity", "entity:delete": "Delete entity", "entity:balance": "View balances", "entity:move": "Move entity to another parent", "country": "Country management", "country:add": "Add country to entity", "country:remove": "Remove country to entity", "currency": "Currency management", "currency:add": "Add currency to entity", "currency:remove": "Remove currency to entity", "domain": "Domain management", "domain:static": "Manage static domain", "domain:static:view": "View static domains", "domain:static:create": "Add static domains", "domain:static:remove": "Remove static domains", "domain:static:edit": "Edit static domains", "domain:dynamic": "Manage dynamic domain", "domain:dynamic:view": "View dynamic domains", "domain:dynamic:create": "Add dynamic domains", "domain:dynamic:remove": "Remove dynamic domains", "domain:dynamic:edit": "Edit dynamic domains", "language": "Language management", "language:add": "Add language to entity", "language:remove": "Remove language to entity", "user": "User management", "user:edit": "Edit user details", "user:view": "View entity's users", "user:create": "Add user to entity", "user:change-state": "Change status of user", "user:change-type": "Change type of user", "user:login-unlock": "Unlock user login", "user:change-password-unlock": "Unlock user changing password", "user:change-password": "Change user password", "user-extra:delete": "Delete user by username", "user-extra:force-reset-password": "Reset user password without old", "user-extra:email:force-set": "Change user email", "permissions": "Manage user permissions", "permissions:view": "View user's permissions", "finance": "Finance management", "finance:debit": "Make debit", "finance:credit": "Make credit", "finance:view": "View debit/credit history", "bi:reports:view": "View available Business Intelligence reports", "bi:report:player": "View players Business Intelligence reports", "bi:report:game": "View games Business Intelligence reports", "bi:report:currency": "View currencies Business Intelligence reports", "bi:report:ggr": "View ggr Business Intelligence reports", "bi:report:grc": "View grc Business Intelligence reports", "bi:report:jackpots": "View jackpots Business Intelligence reports", "player": "Player management", "player:create": "Create player", "player:view": "View player", "player:edit": "Update player", "player:change-state": "Change status of player", "player:login": "Login player", "player:promotion": "Manage player promotions", "player:deposit": "Deposit to player", "player:withdrawal": "<PERSON><PERSON>wal player", "player:change-nickname": "Change player nickname", "responsiblegaming:player": "Manage player's responsible gaming settings", "responsiblegaming:player:view": "View player's responsible gaming settings", "responsiblegaming:player:edit": "Update player's responsible gaming settings", "responsiblegaming:player:delete": "Delete player's responsible gaming pending settings changes", "entity:game": "Manage game", "entity:game:view": "View game", "entity:game:change-state": "Enable disable game for entity", "entity:game:change-state:disabled": "Disable game for entity", "entity:game:change-state:enabled": "Enable game for entity", "entity:game:change-state:enforce": "Allows to change entity game status ignoring the validation of parent entity game status", "entity:game:url": "Get game URL for player", "entity:game:unfinished": "Get unfinished game rounds", "disable:entity:game-history:balances": "Hide balance before balance after in game history", "entitydomain": "Manage entitydomains", "entitydomain:static": "Manage static entitydomains", "entitydomain:static:view": "View static entitydomains", "entitydomain:static:remove": "Remove static entitydomains", "entitydomain:static:create": "Create static entitydomains", "entitydomain:static:edit": "Edit static entitydomains", "entitydomain:dynamic": "Manage dynamic entitydomains", "entitydomain:dynamic:view": "View dynamic entitydomains", "entitydomain:dynamic:remove": "Remove dynamic entitydomains", "entitydomain:dynamic:create": "Create dynamic entitydomains", "entitydomain:dynamic:edit": "Edit dynamic entitydomains", "role": "Manage roles", "role:create": "Create role", "role:edit": "Edit role details", "role:delete": "Delete role", "role:view": "View role", "role:move": "Relocate role", "agent": "Manage agents", "agent:view": "View agents", "site": "Manage site access", "entity:game:history": "Get game history report", "audit": "View audit log", "payment": "Payment methods management", "payment:view": "View payment methods of entity", "payment:edit": "Edit payment method of entity", "payment:create": "Add payment methods of entity", "payment:execute": "Start/init payment on entity", "payment:add-key": "Add public key to gateway", "payment:get-key": "Get public key from gateway", "payment:transfer-in": "Transfering money to player", "payment:transfer-out": "Transfering money from player", "report": "Get Entity brand report", "report:currency": "Get Entity brand currency report based on game rounds", "report:wallet-currency": "Get Entity brand currency report based on wallet data", "report:players": "Get Entity players report", "report:games": "Get Entity games daily report", "report:ggr": "Get Entity ggr total report", "report-without-limit": "Get reports without any limits (like Time limits)", "jurisdiction": "Manage jurisdictions", "jurisdiction:view": "View jurisdictions", "jurisdiction:create": "Create jurisdictions", "jurisdiction:edit": "Update jurisdictions", "jurisdiction:delete": "Delete jurisdictions", "playersession:find": "Find player session", "playersession:kill": "Kill player session", "gamertp": "Manage Game RTP", "gamertp:view": "Game RTP view", "keyentity:gamertp": "Manage Game RTP for key entity", "keyentity:gamertp:view": "Game RTP view for key entity", "keyentity:user": "User management of key entity", "keyentity:user:edit": "Edit user details of key entity", "keyentity:user:view": "View users of key entity", "keyentity:user:create": "Add user to key entity", "keyentity:user:change-state": "Change status of key entity's user", "keyentity:user:change-type": "Change user type for keyentity", "keyentity:user:login-unlock": "Unlock user login", "keyentity:user:change-password-unlock": "Unlock key entity's user changing password", "keyentity:user:change-password": "Change key entity's user password", "keyentity:user-extra:delete": "Delete user by username", "keyentity:user-extra:force-reset-password": "Reset password without old password", "keyentity:user-extra:email:force-set": "Change key entity's user email", "keyentity:permissions": "Manage key entity's user permissions", "keyentity:permissions:view": "View key entity's user permissions", "keyentity:bi:reports": "Manage Business Intelligence reports", "keyentity:bi:reports:view": "View available Business Intelligence reports", "keyentity:bi:report:player": "View players Business Intelligence reports", "keyentity:bi:report:game": "View games Business Intelligence reports", "keyentity:bi:report:currency": "View currencies Business Intelligence reports", "keyentity:bi:report:ggr": "View ggr Business Intelligence reports", "keyentity:bi:report:grc": "View grc Business Intelligence reports", "keyentity:bi:report:jackpots": "View jackpots Business Intelligence reports", "keyentity:player": "Key entity's player management", "keyentity:player:create": "Create player under the key entity", "keyentity:player:view": "View player under the key entity", "keyentity:player:edit": "Update player under the key entity", "keyentity:player:deposit": "Deposit to key entity's player", "keyentity:player:withdrawal": "With<PERSON>wal key entity's player", "keyentity:player:change-state": "Change status of key entity's player", "keyentity:player:login": "Login player under the key entity", "keyentity:player:promotion": "Manage player promotions", "keyentity:player:change-nickname": "Change player nickname under the key entity", "keyentity:gamegroup": "Manage game groups", "keyentity:gamegroup:view": "View game groups", "keyentity:gamegroup:create": "Create gamegroups, manage limits", "keyentity:gamegroup:edit": "Update gamegroups, manage limits", "keyentity:gamegroup:delete": "Remove gamegroups", "keyentity:game": "Manage game", "keyentity:game:view": "View game", "keyentity:game:change-state": "Change status of game", "keyentity:game:change-state:disabled": "Change status of game to disabled", "keyentity:game:change-state:enabled": "Change status of game to enabled", "keyentity:game:limits": "Apply game limit filters", "keyentity:game:url": "Get game URL for player", "keyentity:game:history": "Get game history report", "disable:keyentity:game-history:balances": "Hide balance before balance after in game history for keyentity", "keyentity:gameprovider": "Game provider management", "keyentity:gameprovider:view": "Get list of game providers", "keyentity:gameprovider:create": "Add game provider", "keyentity:gameprovider:change-state": "Change status of game provider", "keyentity:gameprovider:change-secret": "Change game provider secret", "keyentity:gamelabel": "Manage game labels", "keyentity:gamelabel:view": "View game labels", "keyentity:master:gamecategory": "Master manage game categories", "keyentity:master:gamecategory:view": "Master view game categories", "keyentity:gamecategory": "Manage game categories", "keyentity:gamecategory:view": "View game categories", "keyentity:gamecategory:create": "Create game categories", "keyentity:gamecategory:edit": "Update game categories", "keyentity:gamecategory:delete": "Delete game categories", "keyentity:gameprovider:game": "Manage games of gameprovider", "keyentity:gameprovider:game:view": "View games of gameprovider", "keyentity:gameprovider:game:create": "Create game for gameprovider", "keyentity:gameprovider:game:edit": "Update game for gameprovider", "keyentity:gameprovider:game:delete": "Delete game of gameprovider", "keyentity:integration": "Manage integrations", "keyentity:integration:view": "View integrations", "keyentity:integration:create": "Create integrations", "keyentity:integration:edit": "Edit integrations", "keyentity:integration:delete": "Delete integrations", "keyentity:lobby": "Manage lobbies", "keyentity:lobby:view": "View lobbies", "keyentity:lobby:create": "Create lobbies", "keyentity:lobby:edit": "Update lobbies", "keyentity:lobby:delete": "Delete lobbies", "keyentity:terminal": "Manage terminals", "keyentity:terminal:create": "Create terminals", "keyentity:terminal:delete": "Delete terminals", "keyentity:terminal:edit": "Update terminals", "keyentity:terminal:token": "Manage lobby-wrapper tokens", "keyentity:terminal:view": "View terminals", "keyentity:merchant": "Manage merchants of key entity", "keyentity:merchant:create": "Create merchants of key entity", "keyentity:merchant:view": "View merchants of key entity", "keyentity:merchant:edit": "Edit merchants of key entity", "keyentity:report": "Get keyEntity brand report", "keyentity:report:currency": "Get keyEntity brand currency report", "keyentity:report:wallet-currency": "Get Entity brand currency report based on wallet data", "keyentity:report:players": "Get keyEntity players report", "keyentity:report:games": "Get keyEntity games daily report", "keyentity:report:ggr": "Get keyEntity ggr total report", "keyentity:role": "Manage roles of key entity", "keyentity:role:create": "Create role of key entity", "keyentity:role:edit": "Edit role details of key entity", "keyentity:role:delete": "Delete role of key entity", "keyentity:role:view": "View role of key entity", "keyentity:role:move": "Relocate role of key entity", "keyentity:audit": "View audit log", "keyentity:agent": "Manage agents", "keyentity:agent:view": "View agents", "keyentity:site": "Manage site access of entity", "keyentity:payment": "Payment methods management", "keyentity:payment:view": "View payment methods of entity", "keyentity:payment:edit": "Edit payment method of entity", "keyentity:payment:create": "Add payment methods of entity", "keyentity:payment:execute": "Start/init payment on key entity", "keyentity:payment:add-key": "Add public key to gateway", "keyentity:payment:get-key": "Get public key from gateway", "keyentity:payment:transfer-in": "Transfering money to player", "keyentity:payment:transfer-out": "Transfering money from player", "keyentity:notifications": "Manage notifications", "keyentity:notifications:view": "View notifications of key entity", "keyentity:promotion": "Manage keyentity promotions", "keyentity:promotion:skywind": "Manage keyentity skywind promotions", "keyentity:promotion:skywind:create": "Create keyentity skywind promotions", "keyentity:promotion:skywind:edit": "Update keyentity skywind promotions", "keyentity:promotion:owner": "Update owner of keyentity promotion", "keyentity:promotion:view": "View promotions", "keyentity:promotion:create": "Create keyentity promotions", "keyentity:promotion:edit": "Update keyentity promotions", "keyentity:promotion:delete": "Delete keyentity promotions", "keyentity:promotion:bonuscoin": "Manage keyentity bonus coin promo", "keyentity:promotion:freebet": "Manage keyentity freebet promo", "keyentity:promotion:bonuscoin:view": "View keyentity bonus coin promo", "keyentity:promotion:freebet:view": "View keyentity freebet promo", "keyentity:promotion:bonuscoin:create": "Create keyentity bonus coin promotions", "keyentity:promotion:freebet:create": "Create keyentity freebet promotions", "keyentity:promotion:bonuscoin:edit": "Update keyentity bonus coin promotions", "keyentity:promotion:freebet:edit": "Update keyentity freebet promotions", "keyentity:promotion:bonuscoin:delete": "Delete keyentity bonus coin promotions", "keyentity:promotion:freebet:delete": "Delete keyentity freebet promotions", "promotion": "Manage promotions", "promotion:view": "View promotions", "promotion:create": "Create promotions", "promotion:edit": "Update promotions", "promotion:delete": "Delete promotions", "promotion:skywind": "Manage skywind promotions", "promotion:skywind:create": "Create skywind promotions", "promotion:skywind:edit": "Update skywind promotions", "promotion:owner": "Update owner of promotion", "promotion:bonuscoin": "Manage bonus coin promo", "promotion:freebet": "Manage freebet promo", "promotion:bonuscoin:view": "View bonus coin promo", "promotion:freebet:view": "View freebet promo", "promotion:bonuscoin:create": "Create bonus coin promotions", "promotion:freebet:create": "Create freebet promotions", "promotion:bonuscoin:edit": "Update bonus coin promotions", "promotion:freebet:edit": "Update freebet promotions", "promotion:bonuscoin:delete": "Delete bonus coin promotions", "promotion:freebet:delete": "Delete freebet promotions", "keyentity:playersession:find": "Find player session", "keyentity:playersession:kill": "Kill player session", "keyentity:cashier": "Access to Cashier page", "keyentity:entitydomain": "Manage entitydomains", "keyentity:entitydomain:static": "Manage static entitydomains", "keyentity:entitydomain:static:view": "View static entitydomains", "keyentity:entitydomain:static:remove": "Remove static entitydomains", "keyentity:entitydomain:static:create": "Create static entitydomains", "keyentity:entitydomain:static:edit": "Edit static entitydomains", "keyentity:entitydomain:dynamic": "Manage dynamic entitydomains", "keyentity:entitydomain:dynamic:view": "View dynamic entitydomains", "keyentity:entitydomain:dynamic:remove": "Remove dynamic entitydomains", "keyentity:entitydomain:dynamic:create": "Create dynamic entitydomains", "keyentity:entitydomain:dynamic:edit": "Edit dynamic entitydomains", "keyentity:responsiblegaming:player": "Manage player's responsible gaming settings", "keyentity:responsiblegaming:player:view": "View player's responsible gaming settings", "keyentity:responsiblegaming:player:edit": "Update player's responsible gaming settings", "keyentity:responsiblegaming:player:delete": "Delete player's responsible gaming pending settings changes", "merchant": "Manage merchants", "merchant:view": "View merchants", "merchant:edit": "Edit merchants", "merchant:create": "Create merchants", "merchanttypes": "Merchant types management", "merchanttypes:view": "Merchant types view", "merchanttypes:create": "Create merchant type", "merchanttypes:edit": "Update merchant type", "schemadefinition": "Schema definition management", "schemadefinition:view": "Schema definition view", "schemadefinition:create": "Create schema definition", "schemadefinition:edit": "Edit schema definition", "schemadefinition:remove": "Remove schema definition", "schemaconfiguration": "Schema configuration management", "schemaconfiguration:view": "Schema configuration view", "schemaconfiguration:create": "Create schema configuration", "schemaconfiguration:edit": "Edit schema configuration", "schemaconfiguration:remove": "Remove schema configuration", "limittemplate": "Limit template management", "limittemplate:view": "Limit template view", "limittemplate:create": "Create limit template", "limittemplate:edit": "Edit limit template", "limittemplate:remove": "Remove limit template", "settings": "Manage global settings", "jackpot": "Manage jackpot types and instances", "jackpot:type": "Manage jackpot types", "jackpot:type:view": "View jackpot types", "jackpot:type:edit": "Edit jackpot types", "jackpot:type:create": "Create jackpot types", "jackpot:type:delete": "Delete jackpot types", "jackpot:instance": "Manage jackpot instances", "jackpot:instance:view": "View jackpot instances", "jackpot:instance:edit": "Edit jackpot instances", "jackpot:instance:create": "Create jackpot instances", "jackpot:instance:delete": "Delete jackpot instances", "keyentity:jackpot": "Manage keyentity jackpot types and instances", "keyentity:jackpot:instance": "Manage keyentity jackpot instances", "keyentity:jackpot:instance:view": "View keyentity jackpot instances", "keyentity:jackpot:instance:edit": "Edit keyentity jackpot instances", "keyentity:jackpot:instance:create": "Create keyentity jackpot instances", "keyentity:jackpot:instance:delete": "Delete keyentity jackpot instances", "srt": "Manage SRT challenges and tournaments", "srt:challenge": "Manage all challenge feature", "srt:tournament": "Manage all tournament feature", "gs:settings": "Manage GS settings", "gs:settings:view": "View GS settings", "gs:settings:edit": "Edit GS settings", "gs:settings:create": "Create GS settings", "gs:settings:remove": "Remove GS settings", "sw:integration:external": "Manage external integrations", "gamegroup": "Manage game groups", "gamegroup:view": "View game groups", "gamegroup:create": "Create gamegroups, manage limits", "gamegroup:edit": "Update gamegroups, manage limits", "gamegroup:delete": "Remove gamegroups", "merchant:player:gamegroup": "Manage merchant player game groups", "restricted-countries-solution": "Enable disable ability to use countries from jurisdiction for entity", "keyentity:jp-config-report": "View jackpots configuration for key entity", "jp-config-report": "View jackpots configuration", "keyentity:view:game-contexts": "View unfinished game-contexts"}}}, "paths": {"/entities/{path}/phantom/jackpots": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/jpCurrencyRequiredInQuery"}, {"$ref": "#/parameters/requiredPlayerCodeStrictEquality"}, {"$ref": "#/parameters/requiredGameCodeStrictEquality"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["jackpot", "jackpot:instance", "jackpot:instance:view"]}], "tags": ["Phantom"], "summary": "Get phantom Jackpots", "responses": {"200": {"description": "Phantom jackpots", "schema": {"$ref": "#/definitions/PhantomJackpots"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/phantom/jackpots": {"parameters": [{"$ref": "#/parameters/jpCurrencyRequiredInQuery"}, {"$ref": "#/parameters/requiredPlayerCodeStrictEquality"}, {"$ref": "#/parameters/requiredGameCodeStrictEquality"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:jackpot", "keyentity:jackpot:instance", "keyentity:jackpot:instance:view"]}], "tags": ["Phantom"], "summary": "Get phantom Jackpots", "responses": {"200": {"description": "Phantom jackpots", "schema": {"$ref": "#/definitions/PhantomJackpots"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/login": {"post": {"tags": ["User"], "summary": "Logs user in", "description": "Logs user in by entity's secret key and credentials", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"required": ["secret<PERSON>ey", "username", "password"], "properties": {"secretKey": {"type": "string", "description": "secret key", "example": "key"}, "username": {"type": "string", "description": "username", "example": "USER1"}, "password": {"type": "string", "format": "password", "description": "password", "example": "123456qaB"}}}}], "responses": {"200": {"description": "Login information", "schema": {"$ref": "#/definitions/LoginInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 230: User authentication is blocked\n"}, "403": {"description": "- 740: Domain is forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 715: Two Factor Authentication is not set for user\n- 719: An error occurred when sending sms\n- 720: An error occurred when sending email\n"}, "409": {"description": "- 753: Two factor auth code has been sent recently. Repeat attempt a little later\n"}, "500": {"description": "- 719: An error occurred when sending sms\n- 720: An error occurred when sending email\n"}}}}, "/logout": {"post": {"security": [{"apiKey": []}], "tags": ["User"], "summary": "Logs user out", "description": "Logs user out by ending his session.", "responses": {"204": {"description": "Log out successfully executed"}, "400": {"description": "Returned in case we have error on the server side", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n - 204: Access token error\n- 205: Access Token has expired\n- 792: Access Session is expired\n"}}}}, "/users/{userId}/session/{sessionId}": {"parameters": [{"$ref": "#/parameters/pathSessionId"}, {"$ref": "#/parameters/pathUserId"}], "delete": {"security": [{"apiKey": []}], "tags": ["User"], "summary": "Kills user session", "description": "Forcefully logs user out by ending his session.", "responses": {"204": {"description": "Forced log out successfully executed"}, "400": {"description": "Returned in case we have error on the server side", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n - 204: Access token error\n- 205: Access Token has expired\n- 792: Access Session is expired\n"}}}}, "/login/refresh": {"post": {"security": [{"apiKey": []}, {"Permissions": []}], "tags": ["User"], "summary": "Refreshes access token before expiration", "responses": {"200": {"description": "Login information", "schema": {"$ref": "#/definitions/LoginInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/users/{username}/login-lock": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/username"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:login-unlock"]}], "tags": ["User"], "summary": "Unlock user login by path", "responses": {"204": {"description": "User was unlocked"}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 232 User isn't locked\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/users/{username}/login-lock": {"parameters": [{"$ref": "#/parameters/username"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:login-unlock"]}], "tags": ["User"], "summary": "Unlock key entity's user login", "responses": {"204": {"description": "User was unlocked"}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 232 User isn't locked\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/users/{username}/change-password-lock": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/username"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:change-password-unlock"]}], "tags": ["User"], "summary": "Unlock key entity's user changing password", "responses": {"204": {"description": "Changing password for user was unlocked"}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 233 Changing password is available for user\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/users/{username}/change-password-lock": {"parameters": [{"$ref": "#/parameters/username"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:change-password-unlock"]}], "tags": ["User"], "summary": "Unlock key entity's user login", "responses": {"204": {"description": "Changin password for user was unlocked"}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 233 Changing password is available for user\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/currencies": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "Gets key entity currencies list", "description": "Gets list of all available currencies in the key entity", "responses": {"200": {"description": "List of currencies", "schema": {"type": "array", "items": {"$ref": "#/definitions/Currency"}}}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/countries": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:view", "entity"]}], "tags": ["Country"], "summary": "Gets key entity countries list", "description": "Gets list of all available countries in key entity", "responses": {"200": {"description": "List of countries", "schema": {"$ref": "#/definitions/Countries"}}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/languages": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:view", "entity"]}], "tags": ["Language"], "summary": "Get key entity languages list", "description": "Get list of all available languages in key entity", "responses": {"200": {"description": "list of languages", "schema": {"$ref": "#/definitions/Languages"}}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/balances": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:balance", "entity"]}], "tags": ["Entity"], "summary": "Gets key entity list of currencies balances", "responses": {"200": {"description": "List of currencies balances", "schema": {"$ref": "#/definitions/Balances"}}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/games/{gameCode}/info": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:view"]}], "tags": ["Game"], "summary": "Gets key entity's game base info", "description": "Method returns base game information by code (title, type, labels, provider information and so on)", "parameters": [{"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/jpCurrencyOptionalInQuery"}, {"$ref": "#/parameters/addAggregatedFinalLimits"}, {"$ref": "#/parameters/skipJurisdictionFiltering"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/gameGroupNameInQuery"}, {"$ref": "#/parameters/segmentIdInQuery"}], "responses": {"200": {"description": "Game information", "schema": {"$ref": "#/definitions/GameBriefInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 300: Game not found\n"}}}}, "/entities/{path}/games/{gameCode}/info": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:view"]}], "tags": ["Game"], "summary": "Gets key entity's game base info", "description": "Method returns base game information by code (title, type, labels, provider information and so on)", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/jpCurrencyOptionalInQuery"}, {"$ref": "#/parameters/addAggregatedFinalLimits"}, {"$ref": "#/parameters/skipJurisdictionFiltering"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/gameGroupNameInQuery"}, {"$ref": "#/parameters/segmentIdInQuery"}], "responses": {"200": {"description": "Game information", "schema": {"$ref": "#/definitions/GameBriefInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 240: Game not found\n"}}}}, "/entities/{path}/games": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["entity:game"]}], "tags": ["Game"], "summary": "Add games to entity by path", "parameters": [{"$ref": "#/parameters/gameCodes"}], "responses": {"200": {"description": "List of games code", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameShortInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}, "409": {"description": "- 241: Game already exists\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity:game"]}], "tags": ["Game"], "summary": "Remove games from entity by path", "parameters": [{"$ref": "#/parameters/gameCodes"}], "responses": {"200": {"description": "List of games code", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameShortInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}, "409": {"description": "- 303: Failed to delete entity game with child entity games\n"}}}}, "/games/info/search": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:view"]}], "tags": ["Game"], "summary": "Search key entity's games and returns base games info", "description": "Search games for key entity by labels, codes, titles, providers or category and return array of base games' information", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/codeStrictEquality"}, {"$ref": "#/parameters/code__in"}, {"$ref": "#/parameters/jackpots"}, {"$ref": "#/parameters/includeLive"}, {"$ref": "#/parameters/jpCurrencyOptionalInQuery"}, {"$ref": "#/parameters/titleStrictEquality"}, {"$ref": "#/parameters/titleContains"}, {"$ref": "#/parameters/titleNotContains"}, {"$ref": "#/parameters/gameCategoryId"}, {"$ref": "#/parameters/gameProviderId"}, {"$ref": "#/parameters/labelsIdIn"}, {"$ref": "#/parameters/isFreebetSupported"}, {"$ref": "#/parameters/isBonusCoinsSupported"}, {"$ref": "#/parameters/transferEnabled"}, {"$ref": "#/parameters/isGRCGame"}, {"$ref": "#/parameters/jackpotTypes"}, {"$ref": "#/parameters/live"}, {"$ref": "#/parameters/features"}, {"$ref": "#/parameters/limitsCurrencyInQuery"}, {"$ref": "#/parameters/excludeInactiveGames"}], "responses": {"200": {"description": "List of games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameBriefInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/games/live/{provider}/live-info": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:view"]}], "tags": ["Game"], "summary": "Gets key entity's Live Casino game (table) info", "description": "Method returns list of live table games info", "parameters": [{"name": "provider", "in": "path", "type": "string", "description": "Live game provider", "required": true}, {"$ref": "#/parameters/tableId__in"}], "responses": {"200": {"description": "Live game info for tables", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameLive"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 240: Game not found\n"}}}}, "/players": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:create"]}], "tags": ["Player"], "summary": "Creates new player under the key entity", "parameters": [{"$ref": "#/parameters/createPlayer"}], "responses": {"201": {"description": "Created player", "schema": {"$ref": "#/definitions/PlayerInfoExtended"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 82: Country not in list\n- 87: Currency not in list\n- 95: Language not in list\n- 101: Not a brand\n- 503: Merchant brand doesn't support this operation\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group is not found\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:view"]}], "tags": ["Player"], "summary": "Finds players under the specified entity", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/firstNameStrictEquality"}, {"$ref": "#/parameters/firstNameContains"}, {"$ref": "#/parameters/firstNameNotContains"}, {"$ref": "#/parameters/firstNameIn"}, {"$ref": "#/parameters/lastNameStrictEquality"}, {"$ref": "#/parameters/lastNameContains"}, {"$ref": "#/parameters/lastNameNotContains"}, {"$ref": "#/parameters/lastNameIn"}, {"$ref": "#/parameters/emailStrictEquality"}, {"$ref": "#/parameters/emailContains"}, {"$ref": "#/parameters/emailNotContains"}, {"$ref": "#/parameters/emailIn"}, {"$ref": "#/parameters/gameGroupStrictEquality"}, {"$ref": "#/parameters/gameGroupIn"}, {"$ref": "#/parameters/countryStrictEquality"}, {"$ref": "#/parameters/countryIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/lastLogin"}, {"$ref": "#/parameters/lastLogin__gt"}, {"$ref": "#/parameters/lastLogin__lt"}, {"$ref": "#/parameters/createdAt"}, {"$ref": "#/parameters/createdAt__gt"}, {"$ref": "#/parameters/createdAt__lt"}, {"$ref": "#/parameters/updatedAt"}, {"$ref": "#/parameters/updatedAt__gt"}, {"$ref": "#/parameters/updatedAt__lt"}, {"$ref": "#/parameters/status"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/withoutGameGroup"}], "responses": {"200": {"description": "List of players", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerInfoWithBalances"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 101: Not a brand\n- 403: Key is not valid for sort by\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group is not found\n"}}}}, "/players/{playerCode}": {"parameters": [{"$ref": "#/parameters/playerCode"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:view"]}], "tags": ["Player"], "summary": "Gets player information under the key entity", "parameters": [{"$ref": "#/parameters/with<PERSON><PERSON>t"}], "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfoWithBalanceExtended"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 211: Game group is not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:edit"]}], "tags": ["Player"], "summary": "Updates player under the key entity", "parameters": [{"$ref": "#/parameters/updatePlayer"}], "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 82: Country not in list\n- 87: Currency not in list\n- 95: Language not in list\n- 101: Not a brand\n- 225: Player info has not changed\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 211: Game group is not found\n"}}}}, "/players/register": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:create"]}], "tags": ["Player"], "summary": "Creates new player under the key entity", "parameters": [{"$ref": "#/parameters/registerPlayer"}], "responses": {"201": {"description": "Created player", "schema": {"$ref": "#/definitions/PlayerInfoExtended"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 82: Country not in list\n- 87: Currency not in list\n- 95: Language not in list\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group is not found\n"}}}}, "/players/{playerCode}/password": {"parameters": [{"$ref": "#/parameters/playerCode"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:login"]}], "tags": ["Player"], "summary": "Update player password", "description": "Update player password with new password", "parameters": [{"$ref": "#/parameters/updatePlayerPassword"}], "responses": {"200": {"description": "Player info", "schema": {"$ref": "#/definitions/PlayerInfoExtended"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n"}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n- 231: Player change password is blocked\n"}, "404": {"schema": {"$ref": "#/definitions/Error"}, "description": "- 51: Could not find entity\n- 102: Player not found\n"}}}}, "/players/{playerCode}/suspended": {"parameters": [{"$ref": "#/parameters/playerCode"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:change-state"]}], "tags": ["Player"], "summary": "Suspends key entity player", "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfoWithBalances"}}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:change-state"]}], "tags": ["Player"], "summary": "Restores key entity player", "responses": {"200": {"description": "Player information", "schema": {"$ref": "#/definitions/PlayerInfoWithBalances"}}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/player/change-nickname": {"patch": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:change-nickname"]}], "tags": ["Player"], "summary": "Change nickname", "parameters": [{"name": "info", "in": "body", "required": true, "schema": {"type": "object", "required": ["code", "nickname"], "properties": {"code": {"type": "string", "description": "player code", "example": "PL0001"}, "nickname": {"type": "string", "description": "player nickname", "example": "Name"}}}}], "responses": {"204": {"description": "Nickname has been changed successfully"}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/player/change-nickname": {"patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:change-nickname"]}], "tags": ["Player"], "summary": "Change nickname", "parameters": [{"$ref": "#/parameters/path"}, {"name": "info", "in": "body", "required": true, "schema": {"type": "object", "required": ["code", "nickname"], "properties": {"code": {"type": "string", "description": "player code", "example": "PL0001"}, "nickname": {"type": "string", "description": "player nickname", "example": "Name"}}}}], "responses": {"204": {"description": "Nickname has been changed successfully"}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/players/{playerCode}/code-is-used": {"get": {"tags": ["Player"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:view"]}], "summary": "Checks if player code has been already taken", "description": "Checks if player code has been already taken", "parameters": [{"$ref": "#/parameters/playerCode"}], "responses": {"204": {"description": "Player code is not in use"}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 211: Game group is not found\n"}, "409": {"description": "- 750: Player code is in use\n"}}}}, "/players/{email}/mail-is-used": {"get": {"tags": ["Player"], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player", "keyentity:player:view"]}], "summary": "Checks if player email has been already taken", "description": "Checks if player email has been already taken", "parameters": [{"$ref": "#/parameters/email"}], "responses": {"204": {"description": "Player email is not in use"}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 211: Game group is not found\n"}, "409": {"description": "- 199: Email is already used\n"}}}}, "/players/{playerCode}/games/{gameCode}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:url"]}], "tags": ["Game"], "summary": "Gets player game URL", "parameters": [{"$ref": "#/parameters/ip_header"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/ticket"}, {"$ref": "#/parameters/playmode"}, {"$ref": "#/parameters/languageInQuery"}], "responses": {"200": {"description": "Game URL for player", "schema": {"$ref": "#/definitions/PlayerGameURLInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 240: Game not found\n"}, "500": {"description": "- 506: Merchant internal error\n"}}}}, "/fun/games/{gameCode}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:url"]}], "tags": ["Game"], "summary": "Gets game URL", "parameters": [{"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/ticket"}, {"$ref": "#/parameters/languageInQuery"}, {"$ref": "#/parameters/merchantLoginUrl"}], "responses": {"200": {"description": "Game URL for anonymous player", "schema": {"$ref": "#/definitions/PlayerGameURLInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n- 300: Game not found\n"}}}}, "/payments/transfers/in": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:payment", "keyentity:payment:transfer-in"]}], "tags": ["Payments"], "summary": "Transfer to player account", "parameters": [{"$ref": "#/parameters/transferData"}], "responses": {"200": {"description": "Transaction exists", "schema": {"$ref": "#/definitions/PaymentOrderInfo"}}, "201": {"description": "Amount deposited", "schema": {"$ref": "#/definitions/PaymentOrderInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 89: Currency not exist\n- 91: Insufficient balance\n- 101: Not a brand\n- 735: Entity is under maintenance\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/payments/direct/transfers/in": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:payment", "keyentity:payment:transfer-in"]}], "tags": ["Payments"], "summary": "Direct transfer to player account from key entity", "parameters": [{"$ref": "#/parameters/directTransferData"}], "responses": {"200": {"description": "Transaction exists", "schema": {"$ref": "#/definitions/PaymentOrderInfo"}}, "201": {"description": "Amount deposited", "schema": {"$ref": "#/definitions/PaymentOrderInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 89: Currency not exist\n- 729: Entity does not have sufficient balance to perform an operation\n- 101: Not a brand\n- 735: Entity is under maintenance\n- 755: Player default currency doesn't match with requested\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/payments/transfers/out": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:payment", "keyentity:payment:transfer-out"]}], "tags": ["Payments"], "summary": "Transfer from player account", "parameters": [{"$ref": "#/parameters/transferData"}], "responses": {"200": {"description": "Transaction exists", "schema": {"$ref": "#/definitions/PaymentOrderInfo"}}, "201": {"description": "Amount withdrawn", "schema": {"$ref": "#/definitions/PaymentOrderInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 89: Currency not exist\n- 91: Insufficient balance\n- 101: Not a brand\n- 735: Entity is under maintenance\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/payments/direct/transfers/out": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:payment", "keyentity:payment:transfer-out"]}], "tags": ["Payments"], "summary": "Direct transfer from player account to key entity", "parameters": [{"$ref": "#/parameters/directTransferData"}], "responses": {"200": {"description": "Transaction exists", "schema": {"$ref": "#/definitions/PaymentOrderInfo"}}, "201": {"description": "Amount withdrawn", "schema": {"$ref": "#/definitions/PaymentOrderInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 89: Currency not exist\n- 91: Player does not have sufficient balance to perform an operation\n- 101: Not a brand\n- 735: Enti<PERSON> is under maintenance\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/payments": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:payment:view", "keyentity:payment"]}], "tags": ["Payments"], "summary": "Get list of payment order details from keyentity", "description": "This method will return data for a limited time only (default = 3 months). The limit works by 'startDate' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeContains"}, {"$ref": "#/parameters/playerCodeNotContains"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/amount_strict"}, {"$ref": "#/parameters/amount__gt"}, {"$ref": "#/parameters/amount__lt"}, {"$ref": "#/parameters/amount__gte"}, {"$ref": "#/parameters/amount__lte"}, {"$ref": "#/parameters/paymentMethodCodeStrictEquality"}, {"$ref": "#/parameters/paymentMethodCodeIn"}, {"$ref": "#/parameters/orderTypeIn"}, {"$ref": "#/parameters/startDate"}, {"$ref": "#/parameters/startDate__gt"}, {"$ref": "#/parameters/startDate__lt"}, {"$ref": "#/parameters/endDate"}, {"$ref": "#/parameters/endDate__gt"}, {"$ref": "#/parameters/endDate__lt"}, {"$ref": "#/parameters/orderStatus"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/trxId"}, {"$ref": "#/parameters/extTrxId"}], "responses": {"200": {"description": "PaymentOrder info", "schema": {"type": "array", "items": {"$ref": "#/definitions/PaymentOrderInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/payments/{transactionId}": {"parameters": [{"$ref": "#/parameters/transactionId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["payment", "payment:view", "keyentity:payment:view", "keyentity:payment"]}], "tags": ["Payments"], "summary": "Gets transaction status", "responses": {"200": {"description": "Transaction state", "schema": {"$ref": "#/definitions/TransactionInfo"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: User doesn't have permission to execute operation\n"}}}}, "/history/game": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:history"]}], "tags": ["History"], "summary": "Gets game history for the key entity", "description": "This method will return data for a limited time only (default = 3 months). This restriction does not work if you have 'report-without-limit' permission. Note: if round id is in query all other filters are ignored.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/roundIdStrictEquality"}, {"$ref": "#/parameters/roundIdIn"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/firstTs"}, {"$ref": "#/parameters/firstTs__gt"}, {"$ref": "#/parameters/firstTs__gte"}, {"$ref": "#/parameters/firstTs__lt"}, {"$ref": "#/parameters/firstTs__lte"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/queryFinished"}, {"$ref": "#/parameters/bet"}, {"$ref": "#/parameters/bet__lt"}, {"$ref": "#/parameters/bet__lte"}, {"$ref": "#/parameters/bet__gt"}, {"$ref": "#/parameters/bet__gte"}, {"$ref": "#/parameters/win"}, {"$ref": "#/parameters/win__lt"}, {"$ref": "#/parameters/win__lte"}, {"$ref": "#/parameters/win__gt"}, {"$ref": "#/parameters/win__gte"}, {"$ref": "#/parameters/revenue"}, {"$ref": "#/parameters/revenue__lt"}, {"$ref": "#/parameters/revenue__lte"}, {"$ref": "#/parameters/revenue__gt"}, {"$ref": "#/parameters/revenue__gte"}, {"$ref": "#/parameters/device"}, {"$ref": "#/parameters/balanceBefore"}, {"$ref": "#/parameters/balanceBefore__lt"}, {"$ref": "#/parameters/balanceBefore__lte"}, {"$ref": "#/parameters/balanceBefore__gt"}, {"$ref": "#/parameters/balanceBefore__gte"}, {"$ref": "#/parameters/balanceAfter"}, {"$ref": "#/parameters/balanceAfter__lt"}, {"$ref": "#/parameters/balanceAfter__lte"}, {"$ref": "#/parameters/balanceAfter__gt"}, {"$ref": "#/parameters/balanceAfter__gte"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/recoveryType"}, {"$ref": "#/parameters/recoveryType__in"}], "responses": {"200": {"description": "Game history\n####Searchable fields:\n- brandId: Number,\n- roundId: Number,\n- playerCode: String,\n- gameCode: String,\n- currency: String,\n- firstTs: Number,\n- ts: Number,\n- finished: Boolean,\n- bet: Number,\n- win: Number,\n- revenue: Number,\n- isTest: Boolean,\n- recoveryType: String\n", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/history/game/{roundId}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:history"]}], "tags": ["History"], "summary": "Gets round information for the key entity", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/isPayment"}, {"name": "spinNumber", "in": "query", "description": "Spin number", "required": false, "type": "integer"}], "responses": {"200": {"description": "Spins of the round", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistorySpin"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/history/game": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Gets game history for the brand", "description": "The method returns game history items for the brand by its path. Default output is limited to 20 entries per page. Max output is 100 entries per page. This method will return data for a limited time only (default = 3 months). This restriction does not work if you have 'report-without-limit' permission. Note: if round id is in query all other filters are ignored.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/roundIdStrictEquality"}, {"$ref": "#/parameters/roundIdIn"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/firstTs"}, {"$ref": "#/parameters/firstTs__gt"}, {"$ref": "#/parameters/firstTs__gte"}, {"$ref": "#/parameters/firstTs__lt"}, {"$ref": "#/parameters/firstTs__lte"}, {"$ref": "#/parameters/ts"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/queryFinished"}, {"$ref": "#/parameters/bet"}, {"$ref": "#/parameters/bet__lt"}, {"$ref": "#/parameters/bet__lte"}, {"$ref": "#/parameters/bet__gt"}, {"$ref": "#/parameters/bet__gte"}, {"$ref": "#/parameters/win"}, {"$ref": "#/parameters/win__lt"}, {"$ref": "#/parameters/win__lte"}, {"$ref": "#/parameters/win__gt"}, {"$ref": "#/parameters/win__gte"}, {"$ref": "#/parameters/revenue"}, {"$ref": "#/parameters/revenue__lt"}, {"$ref": "#/parameters/revenue__lte"}, {"$ref": "#/parameters/revenue__gt"}, {"$ref": "#/parameters/revenue__gte"}, {"$ref": "#/parameters/device"}, {"$ref": "#/parameters/balanceBefore"}, {"$ref": "#/parameters/balanceBefore__lt"}, {"$ref": "#/parameters/balanceBefore__lte"}, {"$ref": "#/parameters/balanceBefore__gt"}, {"$ref": "#/parameters/balanceBefore__gte"}, {"$ref": "#/parameters/balanceAfter"}, {"$ref": "#/parameters/balanceAfter__lt"}, {"$ref": "#/parameters/balanceAfter__lte"}, {"$ref": "#/parameters/balanceAfter__gt"}, {"$ref": "#/parameters/balanceAfter__gte"}, {"$ref": "#/parameters/isTest"}, {"$ref": "#/parameters/recoveryType"}, {"$ref": "#/parameters/recoveryType__in"}], "responses": {"200": {"description": "Game history\n####Searchable fields:\n- brandId: Number,\n- roundId: Number,\n- playerCode: String,\n- gameCode: String,\n- currency: String,\n- firstTs: Number,\n- ts: Number,\n- finished: Boolean,\n- bet: Number,\n- win: Number,\n- revenue: Number,\n- isTest: Boolean,\n- recoveryType: String\n", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 50: Not master entity\n- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/history/game/{roundId}": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:history"]}], "tags": ["History"], "summary": "Gets round information for the brand", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/isPayment"}, {"name": "spinNumber", "in": "query", "description": "Spin number", "required": false, "type": "integer"}], "responses": {"200": {"description": "Spins of the round", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameHistorySpin"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/history/game/{roundId}/details/{spinNumber}/image": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:history"]}], "tags": ["History"], "summary": "Get game event details visualisation link", "parameters": [{"$ref": "#/parameters/roundId"}, {"$ref": "#/parameters/spinNumber"}], "responses": {"200": {"description": "Link to the spin details visualisation", "schema": {"$ref": "#/definitions/GameHistoryVisualization"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 683: Game history details not found\n- 901: Domain is used by entity\n"}, "409": {"description": "- 689: Game history URL not found\n"}}}}, "/report/currency": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:currency"]}], "tags": ["Reports"], "summary": "Gets currencies report for key entity", "description": "This method will return data for a limited time only (default = 3 months). The limit works by 'ts' field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/from"}, {"$ref": "#/parameters/to"}, {"$ref": "#/parameters/ts__gt"}, {"$ref": "#/parameters/ts__gte"}, {"$ref": "#/parameters/ts__lt"}, {"$ref": "#/parameters/ts__lte"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}], "responses": {"200": {"description": "Currencies report", "schema": {"$ref": "#/definitions/CurrencyReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/report/players": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report", "keyentity:report:players"]}], "tags": ["Reports"], "summary": "Gets players report for key entity", "description": "This method will return data for a limited time only (default = 3 months). The limit works by 'paymentDate', 'paymentDateHour' fields. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/playedGames__gt"}, {"$ref": "#/parameters/playedGames__lt"}, {"$ref": "#/parameters/playedGames__gte"}, {"$ref": "#/parameters/playedGames__lte"}, {"$ref": "#/parameters/playedGames"}, {"$ref": "#/parameters/totalBets__gt"}, {"$ref": "#/parameters/totalBets__lt"}, {"$ref": "#/parameters/totalBets__gte"}, {"$ref": "#/parameters/totalBets__lte"}, {"$ref": "#/parameters/totalBets"}, {"$ref": "#/parameters/totalWins__gt"}, {"$ref": "#/parameters/totalWins__lt"}, {"$ref": "#/parameters/totalWins__gte"}, {"$ref": "#/parameters/totalWins__lte"}, {"$ref": "#/parameters/totalWins"}, {"$ref": "#/parameters/paymentDate"}, {"$ref": "#/parameters/paymentDate__gt"}, {"$ref": "#/parameters/paymentDate__gte"}, {"$ref": "#/parameters/paymentDate__lt"}, {"$ref": "#/parameters/paymentDate__lte"}, {"$ref": "#/parameters/paymentDateHour"}, {"$ref": "#/parameters/paymentDateHour__gt"}, {"$ref": "#/parameters/paymentDateHour__lt"}, {"$ref": "#/parameters/paymentDateHour__gte"}, {"$ref": "#/parameters/paymentDateHour__lte"}], "responses": {"200": {"description": "Players report", "schema": {"$ref": "#/definitions/PlayersReport"}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/report/jackpot/contributions": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report"]}], "tags": ["Reports Jackpot"], "summary": "Gets contributions to jackpots", "description": "Method returns list contributions to jackpot by games. All amounts converted to euros. Sortable fields are \"gameCode\", \"seedAmount\", \"progressiveAmount\", \"totalBetAmount\", \"jpWinAmount\", \"totalBetCount\", \"jpWinCount\", \"firstActivity\", \"lastActivity\". This method will return data for a limited time only (default = 3 months). The limit works by \"dateHour\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/dateHour"}, {"$ref": "#/parameters/dateHour__gt"}, {"$ref": "#/parameters/dateHour__lt"}, {"$ref": "#/parameters/dateHour__gte"}, {"$ref": "#/parameters/dateHour__lte"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}], "responses": {"200": {"description": "Game history", "schema": {"type": "array", "items": {"$ref": "#/definitions/JpContribution"}}}, "400": {"description": "- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}}}}, "/report/jackpot/contributions/players": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report"]}], "tags": ["Reports Jackpot"], "summary": "Gets players' contribution to jackpots", "description": "Method returns list of players with their contributions to games jackpots. All amounts in euros and player's currency. Sortable fields are \"playerCode\", \"gameCode\", \"seedAmount\", \"progressiveAmount\", \"totalBetAmount\", \"jpWinAmount\", \"seedAmountJpCurrency\", \"progressiveAmountJpCurrency\", \"totalBetAmountJpCurrency\", \"jpWinAmountJpCurrency\", \"totalBetCount\", \"jpWinCount\", \"firstActivity\", \"lastActivity\". This method will return data for a limited time only (default = 3 months). The limit works by \"dateHour\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/dateHour"}, {"$ref": "#/parameters/dateHour__gt"}, {"$ref": "#/parameters/dateHour__lt"}, {"$ref": "#/parameters/dateHour__gte"}, {"$ref": "#/parameters/dateHour__lte"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeIn"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/gameCodeIn"}, {"$ref": "#/parameters/currencyStrictEquality"}, {"$ref": "#/parameters/currencyIn"}], "responses": {"200": {"description": "Game history", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerJpContribution"}}}, "400": {"description": "- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}}}}, "/report/jackpot/contributions/logs": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report"]}], "tags": ["Reports Jackpot"], "summary": "Gets jackpot contribution logs", "description": "Method returns list of jackpot contribution logs sorted by trxDate. This method will return data for a limited time only (default = 3 months). The limit works by \"trxDate\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/trxDate__gt"}], "responses": {"200": {"description": "JP contribution log", "schema": {"type": "array", "items": {"$ref": "#/definitions/JpContributionLog"}}}, "400": {"description": "- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}}}}, "/report/jackpot/contributions/wins": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:report"]}], "tags": ["Reports Jackpot"], "summary": "Gets jackpot wins logs", "description": "Method returns list of jackpot wins logs sorted by trxDate. This method will return data for a limited time only (default = 3 months). The limit works by \"trxDate\" field. This restriction does not work if you have 'report-without-limit' permission.", "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/trxDate__gt"}], "responses": {"200": {"description": "JP win logs", "schema": {"type": "array", "items": {"$ref": "#/definitions/JpWinLog"}}}, "400": {"description": "- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 907: Your request took too long time, please change your request\n"}}}}, "/entities/{path}/promo/freebet/{promoId}/players/group": {"put": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/promoId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PlayersFilterData"}}], "security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo"], "summary": "Applys freebet promotion to group of players for a brand", "description": "Method adds freebet promotion to several players according to filtering. If there aren't filters method adds the promotion to all brand's players (so, please think twice befor adding)", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "400": {"description": "- 711: Promo is not in a valid state\n- 687: Error saving promotion transaction operation\n- 688: Promo already has been added to player\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 40: Validation error\n"}}}}, "/promo/freebet/{promoId}/players/group": {"put": {"parameters": [{"$ref": "#/parameters/promoId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PlayersFilterData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Applys freebet promotion to group of players under the key entity", "description": "Method adds freebet promotion to several players according to filtering. If there aren't filters method adds the promotion to all brand's players (so, please think twice befor adding)", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "400": {"description": "- 711: Promo is not in a valid state\n- 687: Error saving promotion transaction operation\n- 688: Promo already has been added to player\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 40: Validation error\n"}}}}, "/players/{playerCode}/freebet/{promoId}": {"put": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Apply freebet promotion to the player", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerFreebetPromotion"}}}, "400": {"description": "- 711: Promo is not in a valid state\n- 688: Promo already has been added to player\n"}, "401": {"description": "- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/players/{playerCode}/promo": {"get": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"name": "type", "in": "query", "description": "type of promotions to fetch. leave blank for all types", "required": false, "type": "string", "enum": ["freebet", "virtual_money", "bonus_coin"]}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Gets list of player promotions", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerFreebetPromotion"}}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/players/{playerCode}/promo/{promoId}": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Get promo rewards for player", "responses": {"200": {"description": "List of current promotions of the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerPromotion"}}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Reset promo rewards for player", "responses": {"204": {"description": "Promo rewards for player was reset"}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/players/{playerCode}/promo/{promoId}/freeBetLeft": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Get promo info and freeBetLeft for player", "responses": {"200": {"description": "Info about player promo", "schema": {"$ref": "#/definitions/PlayerPromotionWithFreeBetLeft"}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/players/{playerCode}/promo": {"get": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"name": "type", "in": "query", "description": "type of promotions to fetch. leave blank for all types", "required": false, "type": "string", "enum": ["freebet", "virtual_money", "bonus_coin"]}], "security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo"], "summary": "Gets list of player promotions by path", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerFreebetPromotion"}}}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/players/{playerCode}/promo/{promoId}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo"], "summary": "Get promo rewards for player by path", "responses": {"200": {"description": "List of current promotions of the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerPromotion"}}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo"], "summary": "Reset promo rewards for player by path", "responses": {"204": {"description": "Promo rewards for player was reset"}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/players/{playerCode}/promo/{promoId}/freeBetLeft": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo"], "summary": "Get promo info and freeBetLeft for player by path", "responses": {"200": {"description": "Info about player promo", "schema": {"$ref": "#/definitions/PlayerPromotionWithFreeBetLeft"}}, "400": {"description": "- 40: Validation error\n- 709: Player has no such promo\n- 710: Can't execute operation for promo with its type\n- 711: Promo is not in a valid state\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/players/{playerCode}/freebet/{promoId}": {"put": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo"], "summary": "Apply freebet promotion to the player by path", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerFreebetPromotion"}}}, "400": {"description": "- 711: Promo is not in a valid state\n- 688: Promo already has been added to player\n"}, "401": {"description": "- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 40: Validation error\n", "schema": {"$ref": "#/definitions/Error"}}}}}, "/entities/{path}/promo/{promoId}/players": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/promoId"}], "get": {"parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeContains"}, {"$ref": "#/parameters/playerCodeNotContains"}, {"$ref": "#/parameters/playerCodeIn"}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:view"]}], "tags": ["Promo"], "summary": "Get promotion players by path", "responses": {"200": {"description": "Promotion found and returned", "schema": {"type": "array", "items": {"$ref": "#/definitions/PromotionPlayer"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 680: Promotion not found\n"}}}, "put": {"parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PlayersFilterData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Add promo players by path", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 706: Can not update promo that is not pending\n"}, "404": {"description": "- 680: Promotion not found\n"}}}, "post": {"parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/AddPromoByPlayersCodesData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Add promo players by path", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 706: Can not update promo that is not pending\n"}, "404": {"description": "- 680: Promotion not found\n"}}}}, "/promo/{promoId}/players": {"parameters": [{"$ref": "#/parameters/promoId"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:view"]}], "parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/playerCodeStrictEquality"}, {"$ref": "#/parameters/playerCodeContains"}, {"$ref": "#/parameters/playerCodeNotContains"}, {"$ref": "#/parameters/playerCodeIn"}], "tags": ["Promo"], "summary": "Get promotion players", "responses": {"200": {"description": "Promotion found and returned", "schema": {"type": "array", "items": {"$ref": "#/definitions/PromotionPlayer"}}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 680: Promotion not found\n"}}}, "put": {"parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PlayersFilterData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Add promo players", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 706: Can not update promo that is not pending\n"}, "404": {"description": "- 680: Promotion not found\n"}}}, "post": {"parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/AddPromoByPlayersCodesData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo"], "summary": "Add promo players", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 706: Can not update promo that is not pending\n"}, "404": {"description": "- 680: Promotion not found\n"}}}}, "/merchants/game/url": {"post": {"tags": ["Merchant"], "summary": "Gets game URL for third party integration", "parameters": [{"$ref": "#/parameters/ip_header"}, {"name": "request", "in": "body", "description": "Game Init request", "required": true, "schema": {"type": "object", "required": ["merchantType", "merchantCode", "gameCode"], "additionalProperties": {"type": "string"}, "properties": {"merchantType": {"type": "string", "description": "merchant integration type", "example": "type1"}, "merchantCode": {"type": "string", "description": "merchant integration code", "example": "code1"}, "gameCode": {"type": "string", "description": "game code", "example": "merchant-game"}}}}], "responses": {"200": {"description": "Game URL for player", "schema": {"$ref": "#/definitions/PlayerGameURLInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 51: Could not find entity\n- 502: Merchant not found\n"}}}}, "/merchants/lobby/url": {"post": {"tags": ["Merchant"], "summary": "Gets lobby URL for third party integration", "parameters": [{"name": "request", "in": "body", "description": "Lobby url request", "required": true, "schema": {"type": "object", "required": ["merchantType", "merchantCode", "ticket"], "properties": {"merchantType": {"type": "string", "description": "merchant integration type", "example": "type1"}, "merchantCode": {"type": "string", "description": "merchant integration code", "example": "code1"}, "ticket": {"type": "string", "description": "merchant’s ticket, used for authentication and getting session ID", "example": "mrch0fwd"}, "language": {"type": "string", "description": "customer language code", "example": "en"}, "lobbyId": {"type": "string", "description": "Lobby public id", "example": "en"}}}}], "responses": {"200": {"description": "Lobby <PERSON> for player", "schema": {"type": "object", "required": ["url"], "properties": {"url": {"type": "string", "description": "lobby URL for specific player", "example": "http://super_lobby.com/"}}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 502: Merchant not found\n"}}}}, "/entities/{path}/players/{playerCode}/session": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/path"}], "delete": {"security": [{"apiKey": []}, {"Permissions": ["playersession:kill"]}], "tags": ["Player"], "summary": "Kill player session by path", "responses": {"202": {"description": "Player session is killed"}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 101: Not a brand\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 707: <PERSON> has exceeded max number of test players\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n"}}}}, "/entities/{path}/history/unfinished/game": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:unfinished"]}], "tags": ["History"], "summary": "Gets unfinished game rounds by entity path", "description": "The method returns unfinished game rounds by entity path.", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/playerCodeRequiredInQuery"}, {"$ref": "#/parameters/gameCodeStrictEquality"}, {"$ref": "#/parameters/firstTs__gt"}, {"$ref": "#/parameters/firstTs__lt"}, {"$ref": "#/parameters/firstTs__gte"}, {"$ref": "#/parameters/firstTs__lte"}, {"$ref": "#/parameters/roundIdInQuery"}, {"$ref": "#/parameters/unfinishedRoundStatus"}, {"$ref": "#/parameters/includeBrokenSpin"}, {"$ref": "#/parameters/gameContextId"}], "responses": {"200": {"description": "Game history\n####Searchable fields:\n- brandId: Number,\n- playerCode: String,\n- gameCode: String,\n- ts: Number,\n- status: String", "schema": {"type": "array", "items": {"$ref": "#/definitions/UnfinishedGameHistory"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 760: An error occurred while querying gameserver\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 900: Domain does not exist"}}}}, "/version": {"get": {"tags": ["Version"], "summary": "Checks service version", "responses": {"200": {"description": "Returns verion, revision and time of build", "schema": {"type": "string", "example": "1.1.1 6ca78adf 01.01.1970 00:00:00"}}}}}, "/players/{playerCode}/password/set-for-unpassworded": {"parameters": [{"$ref": "#/parameters/playerCode"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:login"]}], "tags": ["Player"], "summary": "Set player password", "description": "Set player password for player without password", "parameters": [{"$ref": "#/parameters/setPlayerPassword"}], "responses": {"200": {"description": "Player info", "schema": {"$ref": "#/definitions/PlayerInfoExtended"}}, "400": {"schema": {"$ref": "#/definitions/Error"}, "description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n"}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n- 775: Password already exists\n- 231: Player change password is blocked\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n"}}}}, "/entities/{path}/players/{playerCode}/password/set-for-unpassworded": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["player", "player:login"]}], "tags": ["Player"], "summary": "Set player password by path", "description": "Set player password for player without password", "parameters": [{"$ref": "#/parameters/setPlayerPassword"}], "responses": {"200": {"description": "Player info", "schema": {"$ref": "#/definitions/PlayerInfoExtended"}}, "400": {"schema": {"$ref": "#/definitions/Error"}, "description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n"}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n- 775: Password already exists\n- 231: Player change password is blocked\n"}, "404": {"description": "- 51: Could not find entity\n- 102: Player not found\n"}}}}, "/gamegroups": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamegroup", "keyentity:gamegroup:view"]}], "tags": ["Game Group"], "summary": "Get list of brand's game groups", "responses": {"200": {"description": "List of game groups", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameGroupInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamegroup", "keyentity:gamegroup:create"]}], "tags": ["Game Group"], "summary": "Creates game group", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/GameGroupCreateData"}}], "responses": {"201": {"description": "Created game group", "schema": {"$ref": "#/definitions/GameGroupInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}, "409": {"description": "- 210: Game group already exists\n- 235: Only one default game group per operator is allowed\n"}}}}, "/entities/{path}/gamegroups": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["gamegroup", "gamegroup:view"]}], "tags": ["Game Group"], "summary": "Get list of brand's game groups", "responses": {"200": {"description": "List of game groups", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameGroupInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["gamegroup", "gamegroup:create"]}], "tags": ["Game Group"], "summary": "Creates game group by path", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/GameGroupCreateData"}}], "responses": {"201": {"description": "Created game group", "schema": {"$ref": "#/definitions/GameGroupInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}, "409": {"description": "- 210: Game group already exists\n- 235: Only one default game group per operator is allowed\n"}}}}, "/gamegroups/{gameGroup}": {"parameters": [{"$ref": "#/parameters/gameGroup"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamegroup", "keyentity:gamegroup:view"]}], "tags": ["Game Group"], "summary": "Gets list of games in a game group", "responses": {"200": {"description": "List of games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameCodeInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamegroup", "keyentity:gamegroup:edit"]}], "tags": ["Game Group"], "summary": "Updates game group description", "parameters": [{"in": "body", "name": "info", "description": "Game group description", "required": true, "schema": {"properties": {"description": {"type": "string", "description": "description of Game Group", "example": "VIP-Asia Game Group provides super extended VIP jackpot"}}}}], "responses": {"200": {"description": "Game group information", "schema": {"$ref": "#/definitions/GameGroupInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group not found\n"}, "409": {"description": "- 235: Only one default game group per operator is allowed\n"}}}}, "/entities/{path}/gamegroups/{gameGroup}": {"parameters": [{"$ref": "#/parameters/gameGroup"}, {"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["gamegroup", "gamegroup:view"]}], "tags": ["Game Group"], "summary": "Gets list of games in a game group by path", "responses": {"200": {"description": "List of games", "schema": {"type": "array", "items": {"$ref": "#/definitions/GameCodeInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["gamegroup", "gamegroup:edit"]}], "tags": ["Game Group"], "summary": "Updates game group description by path", "parameters": [{"in": "body", "name": "info", "description": "Game group description", "required": true, "schema": {"properties": {"description": {"type": "string", "description": "description of Game Group", "example": "VIP-Asia Game Group provides super extended VIP jackpot"}}}}], "responses": {"200": {"description": "Game group information", "schema": {"$ref": "#/definitions/GameGroupInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group not found\n"}, "409": {"description": "- 235: Only one default game group per operator is allowed\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["gamegroup", "gamegroup:delete"]}], "tags": ["Game Group"], "parameters": [{"$ref": "#/parameters/forceFlagInQuery"}], "summary": "Delete game group by path", "responses": {"204": {"description": "No body"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group not found\n"}, "409": {"description": "- 236: Game group is default"}}}}, "/gamegroups/{gameGroup}/games/{gameCode}": {"parameters": [{"$ref": "#/parameters/gameGroup"}, {"$ref": "#/parameters/gameCode"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamegroup", "keyentity:gamegroup:edit"]}], "tags": ["Game Group"], "summary": "Adds game to game group", "parameters": [{"name": "limits", "in": "body", "description": "Game limits", "required": true, "schema": {"$ref": "#/definitions/GameGroupLimitsByCurrencyCode"}}], "responses": {"204": {"description": "Game has been added to game group"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 85: Currency not found\n- 211: Game group not found\n- 213: Game doesn't available for entity\n- 214: Game doesn't belong to game group\n"}, "409": {"description": "- 212: Game already exists in game group\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamegroup", "keyentity:gamegroup:edit"]}], "tags": ["Game Group"], "summary": "Updates game's limits", "parameters": [{"name": "limits", "in": "body", "description": "Game limits", "required": true, "schema": {"$ref": "#/definitions/LimitsByCurrencyCode"}}], "responses": {"204": {"description": "Game limits have been updated"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 85: Currency not found\n- 211: Game group not found\n- 213: Game doesn't available for entity\n"}, "409": {"description": "- 212: Game already exists in game group\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamegroup", "keyentity:gamegroup:view"]}], "tags": ["Game Group"], "summary": "Gets game's limits", "responses": {"200": {"description": "Game limits", "schema": {"$ref": "#/definitions/LimitsByCurrencyCode"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group not found\n- 213: Game doesn't available for entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamegroup", "keyentity:gamegroup:edit"]}], "tags": ["Game Group"], "summary": "Removes game from game group", "responses": {"204": {"description": "Game has been removed from then game group"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group not found\n- 213: Game doesn't available for entity\n"}}}}, "/entities/{path}/gamegroups/{gameGroup}/games/{gameCode}": {"parameters": [{"$ref": "#/parameters/gameGroup"}, {"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/path"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["gamegroup", "gamegroup:edit"]}], "tags": ["Game Group"], "summary": "Adds game to game group by path", "parameters": [{"name": "limits", "in": "body", "description": "Game limits", "required": true, "schema": {"$ref": "#/definitions/GameGroupLimitsByCurrencyCode"}}], "responses": {"204": {"description": "Game has been added to game group"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 85: Currency not found\n- 211: Game group not found\n- 213: Game doesn't available for entity\n- 214: Game doesn't belong to game group\n"}, "409": {"description": "- 212: Game already exists in game group\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["gamegroup", "gamegroup:edit"]}], "tags": ["Game Group"], "summary": "Updates game's limits by path", "parameters": [{"name": "limits", "in": "body", "description": "Game limits", "required": true, "schema": {"$ref": "#/definitions/LimitsByCurrencyCode"}}], "responses": {"204": {"description": "Game limits have been updated"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 85: Currency not found\n- 211: Game group not found\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}, "409": {"description": "- 212: Game already exists in game group\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["gamegroup", "gamegroup:view"]}], "tags": ["Game Group"], "summary": "Gets game's limits by path", "responses": {"200": {"description": "Game limits", "schema": {"$ref": "#/definitions/LimitsByCurrencyCode"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group not found\n- 240: Game not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["gamegroup", "gamegroup:edit"]}], "tags": ["Game Group"], "summary": "Removes game from game group by path", "responses": {"204": {"description": "Game has been removed from then game group"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group not found\n- 213: Game doesn't available for entity\n- 240: Game not found\n"}}}}, "/gamegroups/{gameGroup}/games/{gameCode}/limits/{currency}": {"parameters": [{"$ref": "#/parameters/gameGroup"}, {"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/currency"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamegroup", "keyentity:gamegroup:edit"]}], "tags": ["Game Group"], "summary": "Updates game limits by currency code", "parameters": [{"name": "limits", "in": "body", "description": "Game limits", "required": true, "schema": {"$ref": "#/definitions/GameGroupLimits"}}], "responses": {"204": {"description": "Game limits have been updated"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 85: Currency not found\n- 211: Game group not found\n- 213: Game doesn't available for entity\n"}, "409": {"description": "- 212: Game already exists in game group\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:gamegroup", "keyentity:gamegroup:view"]}], "tags": ["Game Group"], "summary": "Gets game's limits", "responses": {"200": {"description": "Game limits", "schema": {"$ref": "#/definitions/GameGroupLimits"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group not found\n- 213: Game doesn't available for entity\n"}, "409": {"description": "- 212: Game already exists in game group\n"}}}}, "/entities/{path}/gamegroups/{gameGroup}/games/{gameCode}/limits/{currency}": {"parameters": [{"$ref": "#/parameters/gameGroup"}, {"$ref": "#/parameters/gameCode"}, {"$ref": "#/parameters/currency"}, {"$ref": "#/parameters/path"}], "put": {"security": [{"apiKey": []}, {"Permissions": ["gamegroup", "gamegroup:edit"]}], "tags": ["Game Group"], "summary": "Updates game limits by currency code by path", "parameters": [{"name": "limits", "in": "body", "description": "Game limits", "required": true, "schema": {"$ref": "#/definitions/GameGroupLimits"}}], "responses": {"204": {"description": "Game limits have been updated"}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 85: Currency not found\n- 211: Game group not found\n- 213: Game doesn't available for entity\n"}, "409": {"description": "- 212: Game already exists in game group\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["gamegroup", "gamegroup:view"]}], "tags": ["Game Group"], "summary": "Gets game's limits by path", "responses": {"200": {"description": "Game limits", "schema": {"$ref": "#/definitions/GameGroupLimits"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 109: Operation is permitted only for brands or merchants\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 211: Game group not found\n- 213: Game doesn't available for entity\n"}, "409": {"description": "- 212: Game already exists in game group\n"}}}}, "/entities": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:create"]}], "tags": ["Entity"], "summary": "Creates a new entity under a specific parent", "parameters": [{"$ref": "#/parameters/createEntity"}], "responses": {"201": {"description": "Created entity", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 107: Parent is brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n"}}}}, "/entities/{path}": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:create"]}], "tags": ["Entity"], "summary": "Creates a new entity under a specific parent path", "parameters": [{"$ref": "#/parameters/createEntity"}], "responses": {"201": {"description": "Created entity", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 107: Parent is brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit"]}], "tags": ["Entity"], "summary": "Updates entity by path", "parameters": [{"$ref": "#/parameters/updateEntity"}], "responses": {"201": {"description": "Updated entity", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entity info by path", "description": "Gets specific entity information, no child entities and including all balances", "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:delete"]}], "tags": ["Entity"], "summary": "Deletes entity by path", "description": "Deletes empty owning entity", "responses": {"200": {"description": "Deleted entity info", "schema": {"$ref": "#/definitions/EntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 64: Enti<PERSON> is not empty!\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/brandentities": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:create"]}], "tags": ["Entity"], "summary": "Creates a new brand under a specific parent", "description": "Create new entity with type \"brand\" under a current parent", "parameters": [{"$ref": "#/parameters/createBrandEntity"}], "responses": {"201": {"description": "Created brand", "schema": {"$ref": "#/definitions/BrandOrMerchantWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 107: Parent is brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n"}}}}, "/brandentities/{path}": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:create"]}], "tags": ["Entity"], "summary": "Creates a new brand under a specific parent", "parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/createBrandEntity"}], "responses": {"201": {"description": "Created brand", "schema": {"$ref": "#/definitions/BrandOrMerchantWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 107: Parent is brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n"}}}}, "/merchantentities": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:create", "keyentity:merchant", "keyentity:merchant:create"]}], "tags": ["Entity"], "summary": "Creates a new merchant entity under a current key entity", "parameters": [{"$ref": "#/parameters/createMerchantEntity"}], "responses": {"201": {"description": "Created entity", "schema": {"$ref": "#/definitions/MerchantEntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 501: Merchant type is not supported\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n- 500: Merchant already exists\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit", "merchant", "merchant:edit"]}], "tags": ["Entity"], "summary": "Updates merchant entity", "parameters": [{"$ref": "#/parameters/updateMerchantEntity"}], "responses": {"201": {"description": "Update merchant entity", "schema": {"$ref": "#/definitions/MerchantEntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 101: Not a brand\n- 504: Not merchant brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view", "keyentity:merchant", "keyentity:merchant:view"]}], "tags": ["Entity"], "summary": "Gets merchant entity info", "description": "Gets current entity info if it is merchant", "responses": {"200": {"description": "Entity information", "schema": {"$ref": "#/definitions/MerchantEntityWithBalances"}}, "400": {"description": "- 504: Not a merchant brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/merchantentities/{path}": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view", "merchant", "merchant:view"]}], "tags": ["Entity"], "summary": "Gets merchant entity info by path", "description": "Gets merchant entity info under given path, including all balances", "responses": {"200": {"description": "Merchant entity information", "schema": {"$ref": "#/definitions/MerchantEntityWithBalances"}}, "400": {"description": "- 504: Not a merchant brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:edit", "merchant", "merchant:edit"]}], "tags": ["Entity"], "summary": "Updates merchant entity by path", "parameters": [{"$ref": "#/parameters/updateMerchantEntity"}], "responses": {"201": {"description": "Update merchant entity", "schema": {"$ref": "#/definitions/MerchantEntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 101: Not a brand\n- 504: Not merchant brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:delete", "merchant", "merchant:edit"]}], "tags": ["Entity"], "summary": "Deletes merchant entity by path", "description": "Deletes empty merchant entity", "responses": {"200": {"description": "Deleted entity info", "schema": {"$ref": "#/definitions/MerchantEntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 64: Entity is not empty!\n- 101: Not a brand\n- 504: Not merchant brand\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:create", "merchant", "merchant:create"]}], "tags": ["Entity"], "summary": "Creates a new merchant entity under a specific parent path", "parameters": [{"$ref": "#/parameters/createMerchantEntity"}], "responses": {"201": {"description": "Created entity", "schema": {"$ref": "#/definitions/MerchantEntityWithBalances"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 61: Parent not found\n- 83: Country not exist in parent\n- 88: Currency not exist in parent\n- 96: Language not exist in parent\n- 97: Countries is not array\n- 98: Currencies is not array\n- 99: Languages is not array\n- 107: Parent is brand\n- 501: Merchant type is not supported\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "409": {"description": "- 60: Entity already exists\n- 500: Merchant already exists\n"}}}}, "/roles": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:role", "keyentity:role:view"]}], "tags": ["Permissions"], "summary": "Gets list of roles", "responses": {"200": {"description": "Role information", "schema": {"type": "array", "items": {"$ref": "#/definitions/ExtendedRoleSchema"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/roles/": {"get": {"security": [{"apiKey": []}, {"Permissions": ["user", "role", "role:view"]}], "tags": ["Permissions"], "summary": "Gets list of roles by path", "parameters": [{"$ref": "#/parameters/path"}], "responses": {"200": {"description": "Role information", "schema": {"type": "array", "items": {"$ref": "#/definitions/ExtendedRoleSchema"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/children/roles/": {"get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:role", "keyentity:role:view"]}], "tags": ["Permissions"], "summary": "Gets list of roles", "responses": {"200": {"description": "Role information", "schema": {"type": "array", "items": {"$ref": "#/definitions/ExtendedRoleSchema"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/children/roles/": {"get": {"security": [{"apiKey": []}, {"Permissions": ["user", "role", "role:view"]}], "tags": ["Permissions"], "summary": "Gets list of children roles by path", "parameters": [{"$ref": "#/parameters/path"}], "responses": {"200": {"description": "Role information", "schema": {"type": "array", "items": {"$ref": "#/definitions/ExtendedRoleSchema"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/users": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:create"]}], "tags": ["User"], "summary": "Creates new user under a specific parent by path", "parameters": [{"$ref": "#/parameters/createUser"}], "responses": {"201": {"description": "Created user", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 625: Add Role to User: failed\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 626: You cannot manage 'rolePid' Role\n"}, "404": {"description": "- 51: Could not find entity\n- 623: Role not exist\n"}, "409": {"description": "- 200: User already exist\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["user", "user:view"]}], "tags": ["User"], "summary": "Finds users under the key entity tree by path", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/username__equal"}, {"$ref": "#/parameters/username__contains"}, {"$ref": "#/parameters/username__contains!"}, {"$ref": "#/parameters/username__in"}, {"$ref": "#/parameters/firstNameStrictEquality"}, {"$ref": "#/parameters/firstNameContains"}, {"$ref": "#/parameters/firstNameNotContains"}, {"$ref": "#/parameters/firstNameIn"}, {"$ref": "#/parameters/lastNameStrictEquality"}, {"$ref": "#/parameters/lastNameContains"}, {"$ref": "#/parameters/lastNameNotContains"}, {"$ref": "#/parameters/lastNameIn"}, {"$ref": "#/parameters/emailStrictEquality"}, {"$ref": "#/parameters/emailContains"}, {"$ref": "#/parameters/emailNotContains"}, {"$ref": "#/parameters/emailIn"}, {"$ref": "#/parameters/lastLogin"}, {"$ref": "#/parameters/lastLogin__gt"}, {"$ref": "#/parameters/lastLogin__lt"}, {"$ref": "#/parameters/createdAt"}, {"$ref": "#/parameters/createdAt__gt"}, {"$ref": "#/parameters/createdAt__lt"}, {"$ref": "#/parameters/updatedAt"}, {"$ref": "#/parameters/updatedAt__gt"}, {"$ref": "#/parameters/updatedAt__lt"}, {"$ref": "#/parameters/status"}, {"$ref": "#/parameters/entity__equal"}, {"$ref": "#/parameters/entity__contains"}, {"$ref": "#/parameters/entity__contains!"}, {"$ref": "#/parameters/entity__in"}, {"$ref": "#/parameters/fullTextSearchQuery"}, {"$ref": "#/parameters/fullTextSearchFields"}], "responses": {"200": {"description": "List of users", "schema": {"type": "array", "items": {"$ref": "#/definitions/DetailedUserInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 403: Key is not valid for sort by\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/users": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:create"]}], "tags": ["User"], "summary": "Creates new user for key entity", "parameters": [{"$ref": "#/parameters/createUser"}], "responses": {"201": {"description": "Created user", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 625: Add Role to User: failed\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 626: You cannot manage 'rolePid' Role\n"}, "404": {"description": "- 51: Could not find entity\n- 623: Role not exist\n"}, "409": {"description": "- 200: User already exist\n"}}}, "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:user", "keyentity:user:view"]}], "tags": ["User"], "summary": "Finds users under the key entity tree", "parameters": [{"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/username__equal"}, {"$ref": "#/parameters/username__contains"}, {"$ref": "#/parameters/username__contains!"}, {"$ref": "#/parameters/username__in"}, {"$ref": "#/parameters/firstNameStrictEquality"}, {"$ref": "#/parameters/firstNameContains"}, {"$ref": "#/parameters/firstNameNotContains"}, {"$ref": "#/parameters/firstNameIn"}, {"$ref": "#/parameters/lastNameStrictEquality"}, {"$ref": "#/parameters/lastNameContains"}, {"$ref": "#/parameters/lastNameNotContains"}, {"$ref": "#/parameters/lastNameIn"}, {"$ref": "#/parameters/emailStrictEquality"}, {"$ref": "#/parameters/emailContains"}, {"$ref": "#/parameters/emailNotContains"}, {"$ref": "#/parameters/emailIn"}, {"$ref": "#/parameters/lastLogin"}, {"$ref": "#/parameters/lastLogin__gt"}, {"$ref": "#/parameters/lastLogin__lt"}, {"$ref": "#/parameters/createdAt"}, {"$ref": "#/parameters/createdAt__gt"}, {"$ref": "#/parameters/createdAt__lt"}, {"$ref": "#/parameters/updatedAt"}, {"$ref": "#/parameters/updatedAt__gt"}, {"$ref": "#/parameters/updatedAt__lt"}, {"$ref": "#/parameters/status"}, {"$ref": "#/parameters/entity__equal"}, {"$ref": "#/parameters/entity__contains"}, {"$ref": "#/parameters/entity__contains!"}, {"$ref": "#/parameters/entity__in"}, {"$ref": "#/parameters/fullTextSearchQuery"}, {"$ref": "#/parameters/fullTextSearchFields"}], "responses": {"200": {"description": "List of users", "schema": {"type": "array", "items": {"$ref": "#/definitions/DetailedUserInfo"}}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 403: Key is not valid for sort by\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "Returned in case we have error on the server side\n- 206: Forbidden\n"}, "404": {"description": "Returned in case we have error on the server side\n- 51: Could not find entity\n"}}}}, "/entities/{path}/users/{username}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/username"}], "get": {"security": [{"apiKey": [], "Permissions": ["user", "user:view"]}], "tags": ["User"], "summary": "Gets user details by path", "responses": {"200": {"description": "Users detailed info", "schema": {"$ref": "#/definitions/DetailedUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}, "patch": {"security": [{"apiKey": [], "Permissions": ["user", "user:edit"]}], "tags": ["User"], "summary": "Changes user details by path", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PatchUserData"}}], "responses": {"200": {"description": "Users detailed info", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 626: You cannot manage 'rolePid' Role\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n- 623: Role not exist\n"}}}, "delete": {"security": [{"apiKey": [], "Permissions": ["user-extra:delete"]}], "tags": ["User"], "summary": "Deletes user by path", "responses": {"204": {"description": "User was deleted"}, "400": {"description": "Returned in case we have error on the server side - 62: One of the parents is suspended", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error - 205: Access Token is expired"}, "403": {"description": "- 206: Forbidden"}, "404": {"description": "- 51: Could not find entity - 198: User does not exist"}}}}, "/users/{username}": {"parameters": [{"$ref": "#/parameters/username"}], "get": {"security": [{"apiKey": [], "Permissions": ["keyentity:user", "keyentity:user:view"]}], "tags": ["User"], "summary": "Gets key user details", "responses": {"200": {"description": "Users detailed info", "schema": {"$ref": "#/definitions/DetailedUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}, "patch": {"security": [{"apiKey": [], "Permissions": ["keyentity:user", "keyentity:user:edit"]}], "tags": ["User"], "summary": "Changes key user details", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PatchUserData"}}], "responses": {"200": {"description": "Users detailed info", "schema": {"$ref": "#/definitions/SummaryUserInfo"}}, "400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 201: Password does not match\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n- 626: You cannot manage 'rolePid' Role\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n- 623: Role not exist\n"}}}, "delete": {"security": [{"apiKey": [], "Permissions": ["keyentity:user-extra:delete"]}], "tags": ["User"], "summary": "Deletes user", "responses": {"204": {"description": "User was deleted"}, "400": {"description": "Returned in case we have error on the server side\n- 62: One of the parents is suspended\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 198: User does not exist\n"}}}}, "/structure": {"get": {"parameters": [{"$ref": "#/parameters/includeProxy"}, {"$ref": "#/parameters/includeMerchantCode"}], "security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entities structure of the related key used", "responses": {"200": {"description": "Entity full hierarchy", "schema": {"$ref": "#/definitions/EntityWithProxy"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/short-structure": {"get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entities short structure of the related key used", "responses": {"200": {"description": "Entity full hierarchy with short info", "schema": {"$ref": "#/definitions/EntityShortInfo"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/structure/move-entity": {"post": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:move"]}], "tags": ["Entity"], "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/MoveEntity"}}], "summary": "Move entity from one to another", "responses": {"400": {"description": "Returned in case we have error on the server side\n- 40: Validation error\n- 83: Country not exist in parent\n- 40: Master can not be moved\n- 40: Child is parent of parent\n- 40: This parent already has child with name\n- 40: <PERSON><PERSON><PERSON> and newParent<PERSON><PERSON> should be different\n- 62: One of the parents is suspended\n- 731: Parent is brand\n- 737: <PERSON><PERSON><PERSON> has conflicts with new parent\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/structure": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/includeProxy"}, {"$ref": "#/parameters/includeMerchantCode"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entity structure including all sub entities by path", "responses": {"200": {"description": "Entity full hierarchy", "schema": {"$ref": "#/definitions/EntityWithProxy"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/short-structure": {"parameters": [{"$ref": "#/parameters/path"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["entity", "entity:view"]}], "tags": ["Entity"], "summary": "Gets entities short structure of the related key used by path", "responses": {"200": {"description": "Entity full hierarchy with short info", "schema": {"$ref": "#/definitions/EntityShortInfo"}}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/players/{playerCode}/bonuscoin/{promoId}": {"put": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Apply bonus coin promotion to the player", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerBonusCoinPromotion"}}}, "400": {"description": "- 40: Validation error\n- 711: Promo is not in a valid state\n- 688: Promo already has been added to player\n"}, "401": {"description": "- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/promo/bonuscoin/{promoId}/players/group": {"put": {"parameters": [{"$ref": "#/parameters/promoId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PlayersFilterData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Applys bonus coin promotion to group of players under the key entity", "description": "Method adds bonus coin promotion to several players according to filtering. If there aren't filters method adds the promotion to all brand's players (so, please think twice before adding)", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "400": {"description": "- 40: Validation error\n- 711: Promo is not in a valid state\n- 687: Error saving promotion transaction operation\n- 688: Promo already has been added to player\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 85: <PERSON><PERSON><PERSON><PERSON> not found\n"}}}}, "/players/{playerCode}/bonuscoin/{promoId}/prolong": {"put": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/BonusCoinProlongData"}}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Prolong bonus coin promotion for the player", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerBonusCoinPromotion"}}}, "400": {"description": "- 40: Validation error\n- 711: Promo is not in a valid state\n- 688: Promo already has been added to player\n"}, "401": {"description": "- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/entities/{path}/players/{playerCode}/bonuscoin/{promoId}": {"put": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}], "security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Apply bonus coin promotion to the player by path", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerBonusCoinPromotion"}}}, "400": {"description": "- 40: Validation error\n- 711: Promo is not in a valid state\n- 688: Promo already has been added to player\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/promo/bonuscoin/{promoId}/players/group": {"put": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/promoId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PlayersFilterData"}}], "security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Applies bonus coin promotion to group of players for a brand by path", "description": "Method adds bonus coin promotion to several players according to filtering. If there aren't filters method adds the promotion to all brand's players (so, please think twice befor adding)", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"$ref": "#/definitions/PlayersPromotionAddStatuses"}}, "400": {"description": "- 40: Validation error\n- 711: Promo is not in a valid state\n- 687: Error saving promotion transaction operation\n- 688: Promo already has been added to player\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 85: <PERSON><PERSON><PERSON>cy not found\n"}}}}, "/entities/{path}/players/{playerCode}/bonuscoin/{promoId}/prolong": {"put": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/promoId"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/BonusCoinProlongData"}}], "security": [{"apiKey": []}, {"Permissions": ["player:promotion"]}], "tags": ["Promo (Deprecated)"], "summary": "Prolong bonus coin promotion for the player by path", "responses": {"200": {"description": "List of promotions applied to the player", "schema": {"type": "array", "items": {"$ref": "#/definitions/PlayerBonusCoinPromotion"}}}, "400": {"description": "- 40: Validation error\n- 711: Promo is not in a valid state\n- 688: Promo already has been added to player\n"}, "401": {"description": "- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/entities/{path}/games/group/limits": {"parameters": [{"$ref": "#/parameters/path"}], "post": {"security": [{"apiKey": []}, {"Permissions": ["entity:game", "entity:game:limits"]}], "tags": ["Game"], "summary": "Sets limitFilters for group of games under a specific brand by path", "parameters": [{"$ref": "#/parameters/gameGroupLimitFilters"}], "responses": {"204": {"description": "Filter limits was changed"}, "400": {"description": "Returned in case we have error on the server side\n- 101: Not a brand\n- 151: Too many items for group action\n- 152: Incorrect action query\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/games/group/limits": {"post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:game", "keyentity:game:limits"]}], "tags": ["Game"], "summary": "Sets limitFilters for group of games for whole structure", "parameters": [{"$ref": "#/parameters/gameGroupLimitFilters"}], "responses": {"204": {"description": "Filter limits was changed"}, "400": {"description": "Returned in case we have error on the server side\n- 101: Not a brand\n- 151: Too many items for group action\n- 152: Incorrect action query\n", "schema": {"$ref": "#/definitions/Error"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}}, "/promo/{promoId}": {"parameters": [{"$ref": "#/parameters/promoId"}, {"$ref": "#/parameters/includePromoTotals"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:view"]}], "tags": ["Promo"], "summary": "Gets a promotion by id", "responses": {"200": {"description": "Promotion found and returned", "schema": {"$ref": "#/definitions/Promotion"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 680: Promotion not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:delete"]}], "tags": ["Promo"], "summary": "Archives promotion", "responses": {"204": {"description": "Successfully archived promotion"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 706: Can not update promo that is not pending\n"}, "404": {"description": "- 680: Promotion not found\n"}}}}, "/promo": {"get": {"parameters": [{"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/promoArchived"}, {"$ref": "#/parameters/promoEnabled"}, {"$ref": "#/parameters/promoStatus"}, {"$ref": "#/parameters/promoState__in"}, {"$ref": "#/parameters/startDate__gte"}, {"$ref": "#/parameters/startDate__lte"}, {"$ref": "#/parameters/endDate__gte"}, {"$ref": "#/parameters/endDate__lte"}, {"$ref": "#/parameters/promoTitle__contains"}, {"$ref": "#/parameters/labelsIdIn"}, {"$ref": "#/parameters/promoType"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/includePromoTotals"}, {"$ref": "#/parameters/owner"}, {"$ref": "#/parameters/promoExternalId"}], "security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:view"]}], "tags": ["Promo"], "summary": "Gets list of promotions", "responses": {"200": {"description": "List of promotions under the key entity tree", "schema": {"type": "array", "items": {"$ref": "#/definitions/PromotionShortInfo"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:create"]}], "tags": ["Promo"], "summary": "Creates promotion", "parameters": [{"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PromotionWithoutIds"}}], "responses": {"201": {"description": "Created promotion info", "schema": {"$ref": "#/definitions/Promotion"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 684: Referenced item is not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["keyentity:promotion", "keyentity:promotion:edit"]}], "tags": ["Promo"], "summary": "Updates promotion", "parameters": [{"name": "info", "in": "body", "schema": {"$ref": "#/definitions/PromotionUpdateData"}}], "responses": {"200": {"description": "Updated promotion info", "schema": {"$ref": "#/definitions/Promotion"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 219: Can not update archived promo\n"}, "404": {"description": "- 680: Promotion not found\n- 684: Referenced item is not found\n"}}}}, "/entities/{path}/promo": {"get": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/offset"}, {"$ref": "#/parameters/limit"}, {"$ref": "#/parameters/sortBy"}, {"$ref": "#/parameters/sortOrder"}, {"$ref": "#/parameters/promoArchived"}, {"$ref": "#/parameters/promoEnabled"}, {"$ref": "#/parameters/promoStatus"}, {"$ref": "#/parameters/promoState__in"}, {"$ref": "#/parameters/startDate__gte"}, {"$ref": "#/parameters/startDate__lte"}, {"$ref": "#/parameters/endDate__gte"}, {"$ref": "#/parameters/endDate__lte"}, {"$ref": "#/parameters/promoTitle__contains"}, {"$ref": "#/parameters/labelsIdIn"}, {"$ref": "#/parameters/promoType"}, {"$ref": "#/parameters/queryFormat"}, {"$ref": "#/parameters/includePromoTotals"}, {"$ref": "#/parameters/owner"}, {"$ref": "#/parameters/promoExternalId"}], "security": [{"apiKey": []}, {"Permissions": ["promotion", "promotion:view"]}], "tags": ["Promo"], "summary": "Gets list of promotions by path", "responses": {"200": {"description": "List of promotions under specified brand", "schema": {"type": "array", "items": {"$ref": "#/definitions/PromotionShortInfo"}}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n"}}}, "post": {"security": [{"apiKey": []}, {"Permissions": ["promotion", "promotion:create"]}], "tags": ["Promo"], "summary": "Creates promotion by path", "parameters": [{"$ref": "#/parameters/path"}, {"in": "body", "name": "info", "required": true, "schema": {"$ref": "#/definitions/PromotionWithoutIds"}}], "responses": {"201": {"description": "Created promotion info", "schema": {"$ref": "#/definitions/Promotion"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 684: Referenced item is not found\n"}}}, "patch": {"security": [{"apiKey": []}, {"Permissions": ["promotion", "promotion:edit"]}], "tags": ["Promo"], "summary": "Updates promotion by path", "parameters": [{"$ref": "#/parameters/path"}, {"name": "info", "in": "body", "schema": {"$ref": "#/definitions/PromotionUpdateData"}}], "responses": {"200": {"description": "Updated promotion info", "schema": {"$ref": "#/definitions/Promotion"}}, "400": {"description": "- 40: Validation error\n"}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 219: Can not update archived promo\n"}, "404": {"description": "- 51: Could not find entity\n- 680: Promotion not found\n- 684: Referenced item is not found\n"}}}}, "/entities/{path}/promo/{promoId}": {"parameters": [{"$ref": "#/parameters/path"}, {"$ref": "#/parameters/promoId"}, {"$ref": "#/parameters/includePromoTotals"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["promotion", "promotion:view"]}], "tags": ["Promo"], "summary": "Gets a promotion by id and path", "responses": {"200": {"description": "Promotion found and returned", "schema": {"$ref": "#/definitions/Promotion"}}, "401": {"description": "- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n"}, "403": {"description": "- 206: Forbidden\n", "schema": {"$ref": "#/definitions/Error"}}, "404": {"description": "- 51: Could not find entity\n- 680: Promotion not found\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["promotion", "promotion:delete"]}], "tags": ["Promo"], "summary": "Archives promotion by path", "responses": {"204": {"description": "Successfully archived promotion"}, "401": {"description": "Returned in case we have error on the server side\n- 10: Access Token is missing\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n- 706: Can not update promo that is not pending\n"}, "404": {"description": "- 51: Could not find entity\n- 680: Promotion not found\n"}}}}, "/merchant/players/gameGroup": {"get": {"security": [{"apiKey": []}, {"Permissions": ["merchant:player:gamegroup"]}], "tags": ["Merchant Game Group"], "summary": "This method allows the Operator to get a list of players along with the game group they’ve been assigned to", "responses": {"200": {"description": "List of players with game codes", "schema": {"$ref": "#/definitions/PlayerGameGroups"}}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/merchant/player/{playerCode}/gameGroup": {"parameters": [{"$ref": "#/parameters/playerCode"}], "get": {"security": [{"apiKey": []}, {"Permissions": ["merchant:player:gamegroup"]}], "tags": ["Merchant Game Group"], "summary": "This method allows the Operator to get Game Group assigned to the player", "responses": {"200": {"description": "Game group", "schema": {"$ref": "#/definitions/PlayerGameGroup"}}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}, "/merchant/player/{playerCode}/gameGroup/{gameGroup}": {"parameters": [{"$ref": "#/parameters/playerCode"}, {"$ref": "#/parameters/gameGroup"}], "patch": {"security": [{"apiKey": []}, {"Permissions": ["merchant:player:gamegroup"]}], "tags": ["Merchant Game Group"], "summary": "This method allows the Operator to assign a player to a particular game group", "responses": {"200": {"description": "Successfully executed"}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}, "delete": {"security": [{"apiKey": []}, {"Permissions": ["merchant:player:gamegroup"]}], "tags": ["Merchant Game Group"], "summary": "This method allows the Operator to get a list of Game Groups available/assigned to the player", "responses": {"200": {"description": "Successfully executed"}, "401": {"description": "Returned in case we have error on the server side\n- 204: Access token error\n- 205: Access Token has expired\n", "schema": {"$ref": "#/definitions/Error"}}, "403": {"description": "- 206: Forbidden\n"}}}}}, "definitions": {"$ref": "./mapi-swagger/definitions.json"}, "parameters": {"$ref": "./mapi-swagger/parameters.json"}}