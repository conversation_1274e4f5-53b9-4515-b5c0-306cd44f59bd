{"swagger": "2.0", "info": {"description": "Skywind - API for Game Provider", "version": "5.55", "title": "Skywind - API for Game Provider"}, "basePath": "/v1", "schemes": ["http", "https"], "produces": ["application/json"], "securityDefinitions": {"provider_user": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-provider-user", "in": "header"}, "provider_code": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-provider-code", "in": "header"}, "provider_secret": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "x-provider-secret", "in": "header"}}, "paths": {"/games/finalize/win": {"put": {"security": [{"provider_user": []}, {"provider_code": []}, {"provider_secret": []}], "tags": ["Game (Deprecated)"], "summary": "Commits player win of unfinished gameround", "deprecated": true, "parameters": [{"name": "paymentRequestWithoutToken", "in": "body", "description": "Payment request (win) without gametoken", "required": true, "schema": {"$ref": "#/definitions/PaymentRequestWithoutToken"}}], "responses": {"200": {"description": "The player's payment has been commited", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 90: Amount is negative\n- 101: Not a brand\n- 107: Invalid game payment\n- 207: Provider user, code or secret is missing\n- 311: Game provider is suspended\n- 735: Entity is under maintenance\n"}, "401": {"description": "- 209: Provider secret incorrect\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 51: Could not find entity\n- 312: Game provider not found\n"}}}}, "/games/finalize/transactionId": {"get": {"security": [{"provider_user": []}, {"provider_code": []}, {"provider_secret": []}], "tags": ["Game (Deprecated)"], "summary": "Generate new transaction id", "deprecated": true, "responses": {"200": {"description": "Response with transactionId that can be used for further payment operation", "schema": {"required": ["transactionId"], "properties": {"transactionId": {"type": "string", "description": "Unique transaction identifier", "example": "ASDJFKJQ2L3I48DFASJDFADSFJ434"}}}}, "400": {"description": "- 207: Provider user, code or secret is missing\n- 311: Game provider is suspended\n"}, "401": {"description": "- 209: Provider secret incorrect\n"}, "404": {"description": "- 312: Game provider not found\n"}}}}, "/games/finalize/rollback": {"put": {"security": [{"provider_user": []}, {"provider_code": []}, {"provider_secret": []}], "tags": ["Game (Deprecated)"], "summary": "Rollbacks player's bet transaction without token", "deprecated": true, "parameters": [{"name": "rollbackWithoutTokenRequest", "in": "body", "description": "Rollback bet transaction", "required": true, "schema": {"$ref": "#/definitions/RollbackWithoutTokenRequest"}}], "responses": {"200": {"description": "The player's payment has been commited", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 92: Bad TransactionId\n- 101: Not a brand\n- 207: Provider user, code or secret is missing\n- 672: Could not find transaction to perform operation.\n- 735: Entity is under maintenance\n"}, "401": {"description": "- 209: Provider secret incorrect\n"}, "404": {"description": "- 51: Could not find entity\n- 240: Game not found\n"}, "500": {"description": "- 673: Not yet ready to rollback transaction. Repeat later.\n"}}}}, "/play/startgame": {"post": {"security": [{"provider_user": []}, {"provider_code": []}, {"provider_secret": []}], "tags": ["Play"], "summary": "Authenticates game server to play game", "parameters": [{"name": "startGameRequest", "in": "body", "description": "Start game request", "required": true, "schema": {"required": ["startGameToken"], "properties": {"startGameToken": {"type": "string", "description": "Start game token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJicmFuZElkIjoxMDAwMiwiZ2FtZUNvZGUiOiJHQU1FMDAxIiwicHJvdmlkZXJDb2RlIjoicHJvdmlkZXJDb2RlMSIsInByb3ZpZGVyR2FtZUNvZGUiOiJHQU1FMDAxIiwicGxheWVyQ29kZSI6MjUwMDEsImN1cnJlbmN5IjoiVVNEIn0.q5-2cnZN1xmXwULz0JlLdxEp7lGL40KWd_XroaWk5WY"}}}}, {"name": "ip", "in": "query", "description": "Player's IP that will be used for IP/country validation", "required": true, "type": "string"}], "responses": {"200": {"description": "The gameserver has been authenticated", "schema": {"required": ["gameToken", "balance"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "balance": {"$ref": "#/definitions/Balance"}}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 240: Game not found\n- 320: Start game token error\n- 321: Start game token is expired\n- 703: IP address cannot be resolved\n- 690: Bonus coins not available\n- 708: It is forbidden to start game from unauthorized site\n- 712: Player is suspended\n- 730: <PERSON><PERSON><PERSON> has different environment id\n- 735: <PERSON><PERSON>ty is under maintenance\n- 751: Refer<PERSON> is missing\n"}, "403": {"description": "- 701: Country of IP is restricted\n- 1500: Can't execute operation. Player is on timeout.\n- 1501: Can't execute operation. Player is self-excluded.\n"}, "404": {"description": "- 85: <PERSON><PERSON><PERSON>cy not found\n- 102: Player not found\n- 104: Limits for currency not found\n"}}}}, "/play/fun/startgame": {"post": {"security": [{"provider_user": []}, {"provider_code": []}, {"provider_secret": []}], "tags": ["Play"], "summary": "Authenticates game server to play fun game", "parameters": [{"name": "startGameRequest", "in": "body", "description": "Start game request. Accepts jwt-token or object", "required": true, "schema": {"required": ["startGameToken"], "properties": {"startGameToken": {"type": "string", "description": "Start game token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJicmFuZElkIjoxMDAwMiwiZ2FtZUNvZGUiOiJHQU1FMDAxIiwicHJvdmlkZXJDb2RlIjoicHJvdmlkZXJDb2RlMSIsInByb3ZpZGVyR2FtZUNvZGUiOiJHQU1FMDAxIiwicGxheWVyQ29kZSI6MjUwMDEsImN1cnJlbmN5IjoiVVNEIn0.q5-2cnZN1xmXwULz0JlLdxEp7lGL40KWd_XroaWk5WY"}}}}], "responses": {"200": {"description": "The gameserver has been authenticated", "schema": {"required": ["gameToken", "balance"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "balance": {"$ref": "#/definitions/Balance"}, "player": {"$ref": "#/definitions/PlayerInfo"}}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 240: Game not found\n- 320: Start game token error\n- 321: Start game token is expired\n- 703: IP address cannot be resolved\n- 712: Player is suspended\n- 730: Entity has different environment id\n- 735: Entity is under maintenance"}, "403": {"description": "- 701: Country of IP is restricted\n- 1500: Can't execute operation. Player is on timeout.\n- 1501: Can't execute operation. Player is self-excluded.\n"}, "404": {"description": "- 85: <PERSON><PERSON><PERSON>cy not found\n- 102: Player not found\n- 104: Limits for currency not found\n"}}}}, "/play/balance": {"get": {"tags": ["Play"], "summary": "Gets player's balance", "parameters": [{"$ref": "#/parameters/gameToken"}], "responses": {"200": {"description": "The player's balance", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 320: Start game token error\n- 321: Start game token is expired\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "404": {"description": "- 733: Player session expired\n"}}}}, "/play/balances": {"get": {"tags": ["Play"], "summary": "Gets all player's balances", "parameters": [{"$ref": "#/parameters/gameToken"}], "responses": {"200": {"description": "All players balances", "schema": {"$ref": "#/definitions/Balances"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 320: Start game token error\n- 321: Start game token is expired\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "404": {"description": "- 733: Player session expired\n"}}}}, "/play/payment": {"put": {"tags": ["Play"], "summary": "Commits player game bet and win", "parameters": [{"name": "paymentRequest", "in": "body", "description": "Payment (win, bet)", "required": true, "schema": {"$ref": "#/definitions/PaymentRequest"}}], "responses": {"200": {"description": "The player's payment has been commited", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 90: Amount is negative\n- 91: Player does not have sufficient balance to perform an operation\n- 92: Bad TransactionId\n- 101: Not a brand\n- 107: Invalid game payment\n- 320: Start game token error\n- 321: Start game token is expired\n- 685: Insufficient free bets balance\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 733: Player session expired\n"}}}}, "/play/payment/bet": {"put": {"tags": ["Play"], "summary": "Commits player game bet", "parameters": [{"name": "betPaymentRequest", "in": "body", "description": "Payment (bet)", "required": true, "schema": {"$ref": "#/definitions/BetOrWinPaymentRequest"}}], "responses": {"200": {"description": "The player's payment has been commited", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 90: Amount is negative\n- 91: Player does not have sufficient balance to perform an operation\n- 92: Bad TransactionId\n- 101: Not a brand\n- 107: Invalid game payment\n- 320: Start game token error\n- 321: Start game token is expired\n- 685: Insufficient free bets balance\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 733: Player session expired\n"}}}}, "/play/payment/win": {"put": {"tags": ["Play"], "summary": "Commits player game win", "parameters": [{"name": "winPaymentRequest", "in": "body", "description": "Payment (win)", "required": true, "schema": {"$ref": "#/definitions/BetOrWinPaymentRequest"}}], "responses": {"200": {"description": "The player's payment has been commited", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 90: Amount is negative\n- 91: Player does not have sufficient balance to perform an operation\n- 92: Bad TransactionId\n- 101: Not a brand\n- 107: Invalid game payment\n- 320: Start game token error\n- 321: Start game token is expired\n- 685: Insufficient free bets balance\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 733: Player session expired\n"}}}}, "/play/payment/rollback": {"put": {"tags": ["Play"], "summary": "Rollbacks player's bet transaction", "parameters": [{"name": "rollbackBetRequest", "in": "body", "description": "Rollback bet transaction", "required": true, "schema": {"$ref": "#/definitions/RollbackBetRequest"}}], "responses": {"200": {"description": "The player's payment has been commited", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 92: Bad TransactionId\n- 101: Not a brand\n- 320: Start game token error\n- 321: Start game token is expired\n- 672: Could not find transaction to perform operation.\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "404": {"description": "- 733: Player session expired\n"}, "500": {"description": "- 673: Not yet ready to rollback transaction. Repeat later."}}}}, "/play/transfer": {"put": {"tags": ["Play"], "summary": "Transfer player's balance", "parameters": [{"name": "transferRequest", "in": "body", "description": "Transfer in/out", "required": true, "schema": {"$ref": "#/definitions/TransferRequest"}}], "responses": {"200": {"description": "The player's transfer has been done", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 91: Player does not have sufficient balance to perform an operation\n- 92: Bad TransactionId\n- 101: Not a brand\n- 320: Start game token error\n- 321: Start game token is expired\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "403": {"description": "- 206: Forbidden\n"}, "500": {"description": "- 668: Transaction is processing\n"}}}}, "/play/payment/transactionId": {"post": {"tags": ["Play"], "summary": "Generates payment transaction identifier for further use in commit payment operation.", "parameters": [{"name": "transactionIdRequest", "in": "body", "description": "TransactionId Request", "required": true, "schema": {"required": ["gameToken"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}}}}], "responses": {"200": {"description": "Response with transactionId that can be used for further payment operation", "schema": {"required": ["transactionId"], "properties": {"transactionId": {"type": "string", "description": "Unique transaction identifier", "example": "ASDJFKJQ2L3I48DFASJDFADSFJ434"}}}}, "400": {"description": "- 40: Validation error\n- 320: Start game token error\n- 321: Start game token is expired"}}}}, "/play/freebet": {"post": {"tags": ["Play"], "summary": "Get free bet info", "parameters": [{"name": "freeBetInfoRequest", "in": "body", "description": "Free bet info fequest", "required": true, "schema": {"required": ["gameToken", "coinMultiplier", "stakeAll"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "coinMultiplier": {"type": "number", "description": "The coin multiplier to get total free bet value (e.g. number of lines in slot games)", "example": 25}, "stakeAll": {"type": "array", "items": {"type": "number"}, "description": "Possible coin values", "example": [0.1, 0.2, 0.5, 1, 2]}}}}], "responses": {"200": {"description": "Response with free bet info", "schema": {"required": ["amount", "coin"], "properties": {"amount": {"type": "number", "description": "Count of available free bets", "example": 10}, "coin": {"type": "number", "description": "Free bet coin per line", "example": 0.1}}}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 101: Not a brand\n- 320: Start game token error\n- 321: Start game token is expired\n- 685: Insufficient free bets balance\n- 686: Invalid free bet\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 733: Player session expired\n"}}}}, "/play/bonusCoins": {"post": {"tags": ["Play"], "summary": "Redeem bonus coins", "parameters": [{"name": "bonusCoinsRedeemRequest", "in": "body", "description": "Bonus coins redeem request", "required": true, "schema": {"required": ["gameToken", "transactionId", "roundId", "promoId", "amount"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "transactionId": {"type": "string", "description": "Unique transaction identifier"}, "extTransactionId": {"type": "string", "description": "external transaction id"}, "roundId": {"type": "number", "description": "round id"}, "promoId": {"type": "string", "description": "promo id"}, "amount": {"type": "number", "description": "Redeem balance amount", "example": 72.5}}}}], "responses": {"200": {"description": "The redeem bonus coins has been done", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 92: Bad TransactionId\n- 101: Not a brand\n- 320: Start game token error\n- 321: Start game token is expired\n- 674: Malformed JSON\n- 691: Insufficient bonus coins balance\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "403": {"description": "- 206: Forbidden\n"}, "404": {"description": "- 733: Player session expired\n"}, "500": {"description": "- 668: Transaction is processing\n"}}}}, "/play/payment/transfer/in": {"put": {"tags": ["Play"], "summary": "Transfer money from internal wallet to game provider", "parameters": [{"name": "externalTransferIn", "in": "body", "description": "Transfer in request", "required": true, "schema": {"$ref": "#/definitions/ExternalTransferInRequest"}}], "responses": {"200": {"description": "Money from internal wallet has been transfered", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 90: Amount is negative\n- 92: Bad TransactionId\n- 101: Not a brand\n- 320: Start game token error\n- 321: Start game token is expired\n- 729: Entity does not have sufficient balance to perform an operation\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/play/payment/transfer/out": {"put": {"tags": ["Play"], "summary": "Return money to internal wallet from game provider", "parameters": [{"name": "externalTransferOut", "in": "body", "description": "Transfer out request", "required": true, "schema": {"$ref": "#/definitions/ExternalTransferOutRequest"}}], "responses": {"200": {"description": "Money to internal wallet has been transfered", "schema": {"$ref": "#/definitions/Balance"}}, "400": {"description": "- 40: Validation error\n- 62: One of the parents is suspended\n- 90: Amount is negative\n- 92: Bad TransactionId\n- 101: Not a brand\n- 320: Start game token error\n- 321: Start game token is expired\n- 729: Entity does not have sufficient balance to perform an operation\n- 730: Entity has different environment id\n- 735: Entity is under maintenance\n"}, "403": {"description": "- 206: Forbidden\n"}}}}, "/play/finalize": {"post": {"tags": ["Play"], "summary": "Finalize game", "parameters": [{"name": "finalizeGameRequest", "in": "body", "description": "Finalize game: log wallet operation and send request to operator to resolve round(optional)", "required": true, "schema": {"$ref": "#/definitions/FinalizeGameRequest"}}], "responses": {"201": {"description": "Accepted"}, "400": {"description": "- 92: Bad TransactionId\n- 730 Entity has different environment id\n- 506 Merchant internal error\n- 322 GameTokenError", "schema": {"$ref": "#/definitions/Error"}}}}}, "/version": {"get": {"tags": ["Version"], "summary": "Checks service version", "responses": {"200": {"description": "Returns verion, revision and time of build", "schema": {"type": "string", "example": "1.1.1 6ca78adf 01.01.1970 00:00:00"}}}}}}, "definitions": {"GameToken": {"type": "string", "description": "Game token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzY290Y2guaW8iLCJleHAiOjEzMDA4MTkzODAsIm5hbWUiOiJDaHJpcyBTZXZpbGxlamEiLCJhZG1pbiI6dHJ1ZX0.03f329983b86f7d9a9f5fef85305880101d5e302afafa20154d094b229f75773"}, "PaymentRequest": {"type": "object", "description": "payment information", "required": ["gameToken", "transactionId", "roundId"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "transactionId": {"type": "string", "description": "Unique transaction identifier"}, "roundId": {"type": "number", "description": "round id"}, "bet": {"type": "number", "description": "Bet"}, "win": {"type": "number", "description": "Win"}, "roundEnded": {"type": "boolean", "description": "true|false - if game round is ended or not"}, "deviceId": {"type": "string", "description": "device id"}, "extTransactionId": {"type": "string", "description": "external transaction id"}}}, "PaymentRequestWithoutToken": {"type": "object", "description": "Win payment information of unfinished gameround", "required": ["code", "brandId", "roundId", "amount", "transactionId", "currency", "gameCode"], "properties": {"code": {"type": "string", "description": "Player code"}, "gameCode": {"type": "string", "description": "Game code"}, "brandId": {"type": "number", "description": "Player brand id"}, "currency": {"type": "string", "description": "Amount currency"}, "transactionId": {"type": "string", "description": "Unique transaction identifier"}, "roundId": {"type": "number", "description": "Round id"}, "amount": {"type": "number", "description": "Bet or Win"}, "roundEnded": {"type": "boolean", "description": "true|false - if game round is ended or not"}, "deviceId": {"type": "string", "description": "Device id"}, "extTransactionId": {"type": "string", "description": "External transaction id"}, "spinType": {"type": "string", "description": "Current spin type", "example": "main", "enum": ["main", "freeGame", "bonus", "reSpin"]}}}, "BetOrWinPaymentRequest": {"type": "object", "description": "bet or win payment information", "required": ["gameToken", "transactionId", "roundId", "amount"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "transactionId": {"type": "string", "description": "Unique transaction identifier"}, "roundId": {"type": "number", "description": "round id"}, "eventId": {"type": "number", "description": "Counter for 'spins' within round (starts from 0). Initial (opening) bet and win of round must have eventId=0. Freespins also count - each freespin bet-win pair must increase this counter."}, "amount": {"type": "number", "description": "Bet or Win"}, "roundEnded": {"type": "boolean", "description": "true|false - if game round is ended or not"}, "deviceId": {"type": "string", "description": "device id"}, "extTransactionId": {"type": "string", "description": "external transaction id"}, "spinType": {"type": "string", "description": "Current spin type", "example": "main", "enum": ["main", "freeGame", "bonus", "reSpin"]}, "isJPWin": {"type": "boolean", "description": "is jackpot win"}}}, "RollbackBetRequest": {"type": "object", "description": "Rollback transaction info", "required": ["gameToken", "extTransactionId", "originalTransactionId", "roundId"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "extTransactionId": {"type": "string", "description": "Transaction id of provider's transaction"}, "originalTransactionId": {"type": "string", "description": "Transaction id of original transaction to rollback"}, "roundId": {"type": "number", "description": "round id"}}}, "RollbackWithoutTokenRequest": {"type": "object", "description": "Rollback transaction info", "required": ["brandId", "gameCode", "extTransactionId", "originalTransactionId", "roundId"], "properties": {"brandId": {"type": "number", "description": "Brand Id"}, "gameCode": {"type": "string", "description": "SW game code"}, "extTransactionId": {"type": "string", "description": "Transaction id of provider's transaction"}, "originalTransactionId": {"type": "string", "description": "Transaction id of original transaction to rollback"}, "roundId": {"type": "number", "description": "round id"}}}, "TransferRequest": {"type": "object", "description": "transfer information", "required": ["gameToken", "transactionId", "roundId"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "transactionId": {"type": "string", "description": "Unique transaction identifier"}, "roundId": {"type": "string", "description": "Game round id"}, "operation": {"type": "string", "description": "transfer-in | transfer-out"}, "amount": {"type": "number", "description": "Amount of transfer"}}}, "PlayerInfo": {"type": "object", "properties": {"code": {"type": "string", "description": "player code", "example": "PLAYER001"}, "status": {"type": "string", "description": "player status", "example": "normal"}, "firstName": {"type": "string", "description": "player first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "player last name", "example": "Dow"}, "email": {"type": "string", "description": "email address", "example": "<EMAIL>"}, "country": {"type": "string", "description": "country code", "example": "US"}, "currency": {"type": "string", "description": "currency code", "example": "USD"}, "language": {"type": "string", "description": "language code", "example": "EN"}, "gameGroup": {"type": "string", "description": "player's game group", "example": "VIP"}}}, "Balance": {"type": "object", "required": ["main"], "properties": {"main": {"type": "number", "description": "balance", "example": 1000}}}, "Balances": {"type": "object", "additionalProperties": {"$ref": "#/definitions/Balance"}, "example": {"USD": {"main": 1000}, "EUR": {"main": 2000}}}, "Error": {"type": "object", "properties": {"code": {"type": "integer", "description": "error code", "example": 677}, "message": {"type": "string", "description": "error message", "example": "Negative transaction operation value"}}}, "ExternalTransferInRequest": {"type": "object", "description": "transfer information", "required": ["gameToken", "transactionId", "roundId"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "transactionId": {"type": "string", "description": "Unique transaction identifier"}, "roundId": {"type": "string", "description": "Round id"}, "amount": {"type": "number", "description": "Amount of transfer"}, "deviceId": {"type": "string", "description": "Platform type web or mobile"}}}, "ExternalTransferOutRequest": {"type": "object", "description": "transfer information", "required": ["gameToken", "transactionId", "roundId"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "transactionId": {"type": "string", "description": "Unique transaction identifier"}, "roundId": {"type": "string", "description": "Round id"}, "amount": {"type": "number", "description": "Amount of transfer"}, "deviceId": {"type": "string", "description": "Platform type web or mobile"}, "actualBetAmount": {"type": "number", "description": "Aggregated summ of all bets in game between last transfer/in and current transfer/out"}, "actualWinAmount": {"type": "number", "description": "Aggregated summ of all wins in game between last transfer/in and current transfer/out"}}}, "FinalizeGameRequest": {"type": "object", "description": "payment information", "required": ["gameToken", "transactionId", "roundId"], "properties": {"gameToken": {"$ref": "#/definitions/GameToken"}, "transactionId": {"type": "string", "description": "Unique transaction identifier"}, "roundId": {"type": "number", "description": "round id"}, "roundPID": {"type": "string", "description": "round public id"}, "roundStatistics": {"$ref": "#/definitions/RoundStatistics"}, "finalizationType": {"type": "string", "enum": ["notSupported", "roundStatistics", "offlinePayments", "forceFinish", "manualPayments"], "example": "forceFinish", "description": "finalization type"}}}, "RoundStatistics": {"type": "object", "properties": {"totalBet": {"type": "number", "description": "Total bet amount that was done for round", "example": 10}, "totalWin": {"type": "string", "description": "Total win amount that was done for round", "example": 100}, "totalJpContribution": {"type": "string", "description": "Total jackpot contribution amount in round", "example": 0.001}, "totalJPWin": {"type": "string", "description": "Total jackpot win amount", "example": 11.1}}}}, "parameters": {"gameToken": {"name": "gameToken", "in": "query", "description": "Game token", "required": true, "type": "string"}, "transactionId": {"name": "transactionId", "in": "path", "description": "Original transaction Id", "type": "string", "required": true}}}