POST {{MAPI_URL}}/v1/login
accept: application/json
Content-Type: application/json
{
    "secretKey": "aaa11200-19f1-48c1-a78c-3a3d56095f38",
    "username": "SUPERADMIN",
    "password": "{{MAPI_PASSWORD}}"
}
HTTP 200
[Captures]
accessToken: jsonpath "$['accessToken']"

## generate terminal token

POST http://localhost:3000/v1/entities/MERCHANT_ENTITY/terminals/token/generate
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
}
HTTP 200
[Captures]
terminalToken: jsonpath "$['terminalToken']"

## create mock merchant player

POST {{MOCK_URL}}/v1/merchant
accept: application/json
Content-Type: application/json
{
  "merch_id": "mrch_ipm",
  "merch_pwd": "Big!Secret01",
  "isPromoInternal": false,
  "multiple_session": false
}

POST {{MOCK_URL}}/v1/merchant/mrch_ipm/customer
accept: application/json
Content-Type: application/json
{
  "cust_id": "Customer123",
  "cust_login": "PLAYER1",
  "currency_code": "USD",
  "language": "en",
  "country": "US",
  "test_cust": false,
  "status": "normal",
  "bet_limit": null,
  "first_name": "",
  "last_name": "",
  "email": "",
  "jurisdiction": null
}

GET http://localhost:8000/v1/merchant/mrch_ipm/customer/Customer123/ticket
accept: text/plain
HTTP 200
[Captures]
ticket: body

POST http://localhost:3004/v1/terminals/players/external/login
accept: application/json
Content-Type: application/json
x-terminal-token: {{terminalToken}}
{
  "ticket": "{{ticket}}"
}
